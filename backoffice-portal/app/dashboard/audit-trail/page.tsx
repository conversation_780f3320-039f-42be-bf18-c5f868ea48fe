import { Divider, Stack, Typography } from '@mui/material'
import React from 'react'

import { AuditTrailIcon } from '@/app/components/SvgIcons/SidebarIcons'

const AuditTrailPage = () => {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <AuditTrailIcon width="28" height="26" />
        <Typography variant="h5">Audit Trail</Typography>
      </Stack>
      <Typography
        variant="h5"
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          marginBottom: '0.5%',
        }}
      ></Typography>
      <Divider />
    </Stack>
  )
}
export default AuditTrailPage
