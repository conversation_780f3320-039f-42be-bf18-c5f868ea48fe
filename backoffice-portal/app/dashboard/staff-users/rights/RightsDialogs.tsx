import {
  <PERSON>,
  <PERSON>,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'
import { CloseRounded, SearchRounded } from '@mui/icons-material'
import { sentenceCase } from 'tiny-case'

import { IRole } from '@/app/interfaces/roles'
import { CustomChip, CustomDrawerChip } from '@/app/components/Chip'
import { CustomSearchInput } from '@/app/components/Input/CustomSearchInput'

export const ViewRoles = ({
  roles,
  permission,
}: {
  roles: IRole[]
  permission: string
}) => {
  const [open, setOpen] = React.useState(false)

  const handleClick = () => {
    setOpen(!open)
  }
  const [search, setSearch] = React.useState('')

  const [filtered, setFiltered] = React.useState<IRole[]>(roles || [])
  const handleSearch = (searchTerm: string) => {
    if (roles) {
      const filteredRoles = roles.filter((role) =>
        role.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFiltered(filteredRoles)
    }
  }
  React.useEffect(() => {
    handleSearch(search)
  }, [search])
  return (
    <>
      <CustomChip
        label={`+${roles.length - 2}`}
        onClick={handleClick}
        key={permission}
      />

      <Drawer open={open} onClose={() => setOpen(false)} anchor="right">
        <Box
          sx={{
            width: '25vw',
            maxHeight: '60px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '16px 20px 8px 24px',
            borderBottom: '1px solid lightgray',
            backgroundColor: '#F9FAFB',
          }}
        >
          {/* DrawerHeader */}
          <Box
            sx={{
              display: 'flex',
              gap: '10px',
              justifyContent: 'flex-start',
              alignItems: 'center',
            }}
          >
            <Typography variant="h6">{sentenceCase(permission)}</Typography>
            {roles && <CustomDrawerChip label={`${roles.length} roles`} />}
          </Box>
          <IconButton
            sx={{
              border: '1px solid #CBD5E1',
              backgroundColor: '#F1F5F9',
            }}
            onClick={() => {
              setOpen(false)
            }}
          >
            <CloseRounded
              sx={{
                fontSize: '20px',
              }}
            />
          </IconButton>
        </Box>
        <Stack
          sx={{
            padding: '19px 36px 0px 36px',
            gap: '26px',
            width: '100%',
            height: '100%',
          }}
        >
          <Box
            sx={{
              position: 'static',
              width: '100%',
            }}
          >
            <CustomSearchInput
              value={search}
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
              ) => {
                const text = e.target.value
                setSearch(text)
              }}
              fullWidth
              sx={{
                width: '100%',
                '&.Mui-focused': {
                  width: '100%',
                  boxShadow: '0px 0px 0px 4px #D3CFDC3D',
                },
              }}
              placeholder="Search"
              startAdornment={<SearchRounded />}
            />
          </Box>
          <Stack>
            <List
              sx={{
                height: '100%',
                overflowX: 'auto',
              }}
            >
              {roles &&
                filtered.map((role) => {
                  return (
                    <Box
                      key={role.id}
                      sx={{
                        padding: 0,
                        margin: 0,
                        background: '',
                      }}
                    >
                      <Chip
                        key={role.id}
                        sx={{
                          padding: '2px 8px',
                          height: '20px',
                          width: 'auto',
                          margin: 0,
                          border: '1px solid #E3E4E4',
                          background: '#F8F9FC',
                          fontSize: '12px',
                          fontWeight: '400',
                          lineHeight: '20px',
                          '& .MuiChip-label': {
                            padding: 0,
                          },
                        }}
                        label={role.name}
                      />
                    </Box>
                  )
                })}
            </List>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
