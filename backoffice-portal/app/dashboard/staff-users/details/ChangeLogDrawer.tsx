import {
  Avatar,
  Box,
  <PERSON><PERSON>,
  Click<PERSON>way<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>row,
  IconButton,
  List,
  ListItem,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  styled,
  Typography,
} from '@mui/material'
import React from 'react'
import {
  Close,
  FilterList,
  KeyboardArrowDownRounded,
  SearchRounded,
} from '@mui/icons-material'

import { CustomSearchInput } from '@/app/components/Input/CustomSearchInput'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { setOpenUserChangeLogs } from '@/app/redux/reducers/overlays'

import { Changes, changes } from '../../customers/customer/Details/changesList'

const Span = styled('span')(
  () =>
    `
  color: #000A12;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  text-wrap: wrap;
    `
)

const renderChangeItem = (change: Changes) => {
  return (
    <Box
      sx={{
        width: '100%',
      }}
    >
      {change.action === 'create' && change.type === 'creation' && (
        <Typography variant="body2" sx={{ whiteSpace: 'normal' }}>
          <Span>{change.actionee}</Span> {'created'}{' '}
          <Span>{change.actionSubject} </Span>
        </Typography>
      )}

      {change.action === 'edit' && change.type === 'approval' && (
        <Typography
          variant="body2"
          sx={{ whiteSpace: 'normal', textWrap: 'nowrap' }}
        >
          <Span>{change.actionee} </Span>approved {change.resource} change from{' '}
          <Span>{change.previousState}</Span> to{' '}
          <Span>{change.actionSubject}</Span>
        </Typography>
      )}

      {change.action === 'edit' && change.type === 'edited' && (
        <Typography>
          <Span>{change.actionee} </Span>changed {change.resource} from{' '}
          <Span>{change.previousState}</Span> to{' '}
          <Span>{change.actionSubject}</Span>.
        </Typography>
      )}
      {change.action === 'create' && change.type === 'approval' && (
        <Typography>
          <Span>{change.actionee}</Span> approved creation of{' '}
          <Span>{change.actionSubject}</Span>.
        </Typography>
      )}
      {change.action === 'delete' && change.type === 'deletion' && (
        <Typography>
          <Span>{change.actionee}</Span> deleted{' '}
          <Span>{change.actionSubject}</Span>.
        </Typography>
      )}
      {change.action === 'delete' && change.type === 'approval' && (
        <Typography>
          <Span>{change.actionee}</Span> approved deletion of{' '}
          <Span>{change.actionSubject}</Span>.
        </Typography>
      )}
      {change.comment && (
        <Box
          sx={{
            width: '20vw',
            display: 'flex',
            flexDirection: 'column',
            padding: '10px 14px',
            border: '1px solid #EEEEEF',
            borderRadius: '4px',
          }}
        >
          <Typography
            variant="body3"
            sx={{
              color: '#6B7280',
              fontWeight: 500,
            }}
          >
            Comment
          </Typography>
          <Typography
            variant="body3"
            sx={{
              fontWeight: 400,
            }}
          >
            {change.comment}{' '}
          </Typography>
        </Box>
      )}

      <Typography
        variant="body3"
        sx={{
          color: '#6B7280',
          fontWeight: 500,
        }}
      >
        {change.date}
        {' | '}
        {change.time}
      </Typography>
    </Box>
  )
}

const ChangeLogDrawer = () => {
  const openDrawer = useAppSelector(
    (state) => state.overlay.openUserChangeLogDrawer
  )
  const [openFilter, setOpenFilter] = React.useState<boolean>(false)
  const dispatch = useAppDispatch()

  // filter dropdown
  const [open, setOpen] = React.useState(false)
  const [openSub, setOpenSub] = React.useState(false)
  const anchorSubRef = React.useRef<HTMLButtonElement>(null)
  const anchorRef = React.useRef<HTMLButtonElement>(null)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
    setOpenSub(false)
  }
  const handleToggleSub = () => {
    setOpenSub((prevOpenSub) => !prevOpenSub)
    setOpen(false)
  }

  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    if (
      anchorSubRef.current &&
      anchorSubRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }

    setOpen(false)
    setOpenSub(false)
  }

  function handleListKeyDown(event: React.KeyboardEvent) {
    if (event.key === 'Tab') {
      event.preventDefault()
      setOpen(false)
      setOpenSub(false)
    } else if (event.key === 'Escape') {
      setOpen(false)
      setOpenSub(false)
    }
  }

  // return focus to the button when we transitioned from !open -> open
  const prevOpen = React.useRef(open)
  const prevOpenSub = React.useRef(openSub)
  React.useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current!.focus()
    }
    if (prevOpenSub.current === true && openSub === false) {
      anchorSubRef.current!.focus()
    }

    prevOpen.current = open
    prevOpenSub.current = openSub
  }, [open, openSub])

  return (
    <Drawer open={openDrawer} anchor="right">
      <Paper
        elevation={0}
        sx={{
          width: '32vw',
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'hidden',
        }}
      >
        <Box sx={{}}>
          {/* header */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              padding: '16px 20px 8px 29px',
              borderBottom: '2px solid #F2F4F7',
              background: '#F9FAFB',
            }}
          >
            <Typography variant="h5">Changes Log</Typography>
            <IconButton
              onClick={() => {
                dispatch(setOpenUserChangeLogs(false))
              }}
              sx={{
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                border: ' 1px solid #CBD5E1',
                background: '#F1F5F9',
              }}
            >
              <Close
                sx={{
                  width: '20px',
                  height: '20px',
                }}
              />
            </IconButton>
          </Box>

          {/* search box and filter */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              padding: '0px 20px 0px 29px',
              width: '100%',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '30px 0px 8px 0px',
                gap: '10px',
              }}
            >
              <CustomSearchInput
                startAdornment={<SearchRounded />}
                placeholder="Search"
                sx={{
                  height: '40px',
                }}
              />
              <Button
                size="small"
                variant="outlined"
                onClick={() => setOpenFilter(!openFilter)}
                sx={{
                  display: 'flex',
                  minWidth: '131px',
                  padding: '8px 42px',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '10px',
                  height: '40px',
                  borderRadius: '4px',
                  border: '1.5px solid #D0D5DD',
                  background: '#FFF',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
              >
                <FilterList />
                <Typography>Filter</Typography>
              </Button>
            </Box>
            {/* Filter option render conditionally */}
            {openFilter && (
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  gap: '12px',
                }}
              >
                <Box>
                  <Button
                    sx={{
                      height: '34px',
                      width: '156px',
                      textWrap: 'nowrap',
                      padding: '9px 28px',
                      borderRadius: '4px',
                      border: '1px solid  #AAADB0',
                      background: ' #FFF',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    variant="outlined"
                    ref={anchorRef}
                    id="composition-button"
                    aria-controls={open ? 'composition-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleToggle}
                    endIcon={<KeyboardArrowDownRounded />}
                  >
                    <Typography variant="label1">Date Modified</Typography>
                  </Button>
                  <Popper
                    open={open}
                    anchorEl={anchorRef.current}
                    role={undefined}
                    placement="bottom-start"
                    transition
                    disablePortal
                    sx={{
                      zIndex: '2000',
                    }}
                  >
                    {({ TransitionProps, placement }) => (
                      <Grow
                        {...TransitionProps}
                        style={{
                          transformOrigin:
                            placement === 'bottom-start'
                              ? 'left top'
                              : 'left bottom',
                        }}
                      >
                        <Paper
                          sx={{
                            minWidth: '156px',
                            marginTop: '22px',
                            padding: '0px 0px 12px 0px',
                            borderRadius: '8px',
                            border: '1px solid #ECECEC',
                            background: '#FFF',
                            boxShadow:
                              '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                          }}
                        >
                          <ClickAwayListener onClickAway={handleClose}>
                            <MenuList
                              autoFocusItem={open}
                              id="composition-menu"
                              aria-labelledby="composition-button"
                              onKeyDown={handleListKeyDown}
                            >
                              <MenuItem onClick={handleClose}>
                                {'Last 24 hours'}
                              </MenuItem>
                            </MenuList>
                          </ClickAwayListener>
                        </Paper>
                      </Grow>
                    )}
                  </Popper>
                </Box>
                <Box>
                  <Button
                    sx={{
                      height: '34px',
                      width: '156px',
                      textWrap: 'nowrap',
                      padding: '9px 28px',
                      borderRadius: '4px',
                      border: '1px solid  #AAADB0',
                      background: ' #FFF',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    variant="outlined"
                    ref={anchorSubRef}
                    id="composition-button"
                    aria-controls={openSub ? 'composition-menu' : undefined}
                    aria-expanded={openSub ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleToggleSub}
                    endIcon={<KeyboardArrowDownRounded />}
                  >
                    <Typography variant="label1">Sub module</Typography>
                  </Button>
                  <Popper
                    open={openSub}
                    anchorEl={anchorSubRef.current}
                    role={undefined}
                    placement="bottom-start"
                    transition
                    disablePortal
                    sx={{
                      zIndex: '2000',
                    }}
                  >
                    {({ TransitionProps, placement }) => (
                      <Grow
                        {...TransitionProps}
                        style={{
                          transformOrigin:
                            placement === 'bottom-start'
                              ? 'left top'
                              : 'left bottom',
                        }}
                      >
                        <Paper
                          sx={{
                            minWidth: '156px',
                            marginTop: '22px',
                            padding: '0px 0px 12px 0px',
                            borderRadius: '8px',
                            border: '1px solid #ECECEC',
                            background: '#FFF',
                            boxShadow:
                              '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                          }}
                        >
                          <ClickAwayListener onClickAway={handleClose}>
                            <MenuList
                              autoFocusItem={openSub}
                              id="composition-menu"
                              aria-labelledby="composition-button"
                              onKeyDown={handleListKeyDown}
                            >
                              <MenuItem onClick={handleClose}>User</MenuItem>
                            </MenuList>
                          </ClickAwayListener>
                        </Paper>
                      </Grow>
                    )}
                  </Popper>
                </Box>
              </Box>
            )}
          </Box>
        </Box>

        {/* listbox */}
        <Box
          sx={{
            flex: 1,
            overflowY: 'auto',
            scrollbarWidth: 'thin',
          }}
        >
          <List
            sx={{
              width: '100%',
            }}
          >
            {changes &&
              changes.map((change: Changes, index) => {
                return (
                  <ListItem
                    key={index}
                    sx={{
                      padding: '16px 29px',
                      display: 'flex',
                      flexDirection: 'row',
                      gap: '17px',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-start',
                      border: '1px solid #E3E4E4',
                      width: '100%',
                    }}
                  >
                    <Avatar>
                      <Typography>
                        {change?.actionee &&
                          change.actionee.split(' ')[0][0] +
                            change.actionee.split(' ')[
                              change.actionee.split(' ').length - 1
                            ][0]}
                      </Typography>
                    </Avatar>
                    {renderChangeItem(change)}
                  </ListItem>
                )
              })}
          </List>
        </Box>
      </Paper>
    </Drawer>
  )
}

export default ChangeLogDrawer
