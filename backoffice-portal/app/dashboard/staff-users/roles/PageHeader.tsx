'use client'
import { CircularProgress, Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'

import { CustomFilterBox } from '@/app/components/Input/CustomFilterBox'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  setRoleFilterValue,
  setRoleSearchValue,
} from '@/app/redux/reducers/rolesReducer'
import ExportButton from '@/app/components/ExportButton/ExportButton'
import ExportPreferences from '@/app/components/Dialogs/ExportPreferences'
import { setNotification } from '@/app/redux/reducers/notifications'
import { FileFormat, generateRoleReports } from '@/app/redux/actions/roles'
import { IFilter, IFilterOption } from '@/app/interfaces/shared'

import { CreateRole } from './CreateRole'

interface PageHeaderProps {
  selectedUserCount: number
  selectedIds: string[]
  search: (value: string) => void
  filter: (value: Record<string, string | string[]>) => void
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  selectedUserCount,
  selectedIds,
  search,
  filter,
}) => {
  const dispatch = useAppDispatch()
  const { rolesCount } = useAppSelector((state) => state.roles)
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = React.useState(false)
  const modules = useAppSelector((state) => state.roles.permissionGroup)
  const [searchValue, setSearchValue] = useState<string>('')
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    dispatch(setRoleSearchValue(e.target.value))
    search(e.target.value)
  }
  const handleFilterChange = (filters: Record<string, string | string[]>) => {
    dispatch(setRoleFilterValue(filters))
    filter(filters)
  }
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [exportCount, setExportCount] = useState<number>(0)
  const filters: IFilter[] = [
    {
      filterName: 'Is Visible',
      options: [
        { key: 'yes', value: 'yes', label: 'Yes' },
        { key: 'no', value: 'no', label: 'No' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Module',
      options: [
        ...modules.map((module) => {
          return {
            key: module.name,
            value: module.name,
            label: sentenceCase(module.name),
          }
        }),
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Rights',
      options: [
        ...modules.map((module) => {
          const options = module.permissions.map((permission) => {
            return {
              key: permission.name,
              value: permission.name,
              label: sentenceCase(permission.name),
            }
          })
          return options
        }),
      ].flat(Infinity) as IFilterOption[],
      type: 'dropdown/single',
    },
  ]
  useEffect(() => {
    setExportCount(rolesCount || 0)
  }, [rolesCount])

  const handleExportClick = () => {
    setOpen(true)
  }
  const handleExport = async (format: FileFormat) => {
    setOpen(false)
    setLoading(true)

    const filteredData = selectedIds.map((id) => ({ id }))

    // Export logic based on selected format
    await generateRoleReports({
      dispatch,
      params: {
        page: 1,
        size: 10,
        Rights: '',
        Modules: '',
        UsersAssigned: '',
      },
      format: format,
      filteredData: filteredData,
    })

    // Simulate export process
    await new Promise((resolve) => setTimeout(resolve, 3000))
    setLoading(false)
    dispatch(
      setNotification({
        message: `${selectedUserCount > 0 ? `${selectedUserCount} ${selectedUserCount === 1 ? 'item' : 'items'}` : `${exportCount} items`} successfully exported.`,
        type: 'success',
      })
    )
  }

  const handleCancel = () => {
    setOpen(false)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        my: '17px',
      }}
    >
      <Stack
        sx={{
          flexGrow: 1,
        }}
      >
        <CustomFilterBox
          openFilter={openFilter}
          setOpenFilter={setOpenFilter}
          searchValue={searchValue}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </Stack>
      <Stack
        sx={{
          gap: '5%',
          flexDirection: 'row',
        }}
      >
        <ExportButton
          onClick={handleExportClick}
          text={
            openFilter
              ? selectedUserCount > 0
                ? `Export ${selectedUserCount} ${selectedUserCount === 1 ? 'item' : 'items'}`
                : `Export All ${exportCount}`
              : `Export All`
          }
          openFilter={openFilter}
        />
        <Stack>
          <CreateRole />
        </Stack>
      </Stack>
      <ExportPreferences
        open={open}
        onExport={handleExport}
        onCancel={handleCancel}
        setOpen={setOpen}
        selectedIds={[]}
      />

      {loading && (
        <Stack
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.5)',
            zIndex: 10,
          }}
        >
          <CircularProgress size={40} sx={{ color: '#1976d2' }} />
        </Stack>
      )}
    </Stack>
  )
}
