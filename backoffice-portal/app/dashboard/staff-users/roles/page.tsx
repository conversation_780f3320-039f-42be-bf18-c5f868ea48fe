'use client'
import React, { useCallback, useEffect, useState } from 'react'
import { Stack } from '@mui/system'
import _ from 'lodash'

import { useAppDispatch, useAppSelector } from '@/app/redux'
import { EmptyRoles } from '@/app/dashboard/staff-users/roles/EmptyRoles'
import { ListRoles } from '@/app/dashboard/staff-users/roles/ListRoles'
import { PageHeader } from '@/app/dashboard/staff-users/roles/PageHeader'
import { getPermissionsGroup, getRolesFilter } from '@/app/redux/actions/roles'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'

const RolesPage = () => {
  const dispatch = useAppDispatch()
  const [selectedUserCount, setSelectedUserCount] = useState<number>(0)
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const rolesList = useAppSelector((state) => state.roles.rolesList)
  const isLoadingRoles = useAppSelector((state) => state.roles.isLoadingRoles)
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [filterValue, setFilterValue] = useState<
    Record<string, string | string[]>
  >({})
  const [rowsPerPage] = useState(10)
  useEffect(() => {
    getRolesFilter(dispatch, { page: 1, size: 10 })
    getPermissionsGroup(dispatch)
  }, [])
  const payload = {
    page,
    size: rowsPerPage,
    roleName: search,
    isVisible: filterValue['Is Visible'],
    moduleName: filterValue.Module,
    rights: filterValue.Rights,
  }
  const debouncedSearch = useCallback(
    _.debounce(
      (value: string, filter: Record<string, string | string[]>) =>
        getRolesFilter(dispatch, {
          ...payload,
          page: 1,
          roleName: value,
          isVisible: filter['Is Visible'],
          moduleName: filter.Module,
          rights: filter.Rights,
        }),
      1000
    ),
    []
  )
  useEffect(() => {
    getRolesFilter(dispatch, payload)
  }, [page])
  const triggerSearch = (searchValue: string) => {
    setSearch(searchValue)
    debouncedSearch(searchValue, filterValue)
  }
  const triggerFilter = (filter: Record<string, string | string[]>) => {
    setFilterValue(filter)
    setPage(1)
    getRolesFilter(dispatch, {
      ...payload,
      page: 1,
      isVisible: filter['Is Visible'],
      moduleName: filter.Module,
      rights: filter.Rights,
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        py: '0.5%',
        px: '1.5%',
      }}
    >
      <PageHeader
        search={triggerSearch}
        filter={triggerFilter}
        selectedUserCount={selectedUserCount}
        selectedIds={selectedIds}
      />
      {isLoadingRoles ? (
        <CustomSkeleton
          animation="pulse"
          variant="rectangular"
          width={'100%'}
          height={'60vh'}
        />
      ) : (
        <>
          {rolesList && rolesList.length === 0 ? (
            <EmptyRoles />
          ) : (
            <>
              <ListRoles
                page={page}
                setPage={setPage}
                onSelectedCountChange={setSelectedUserCount}
                onExport={setSelectedIds}
              />
            </>
          )}
        </>
      )}
    </Stack>
  )
}

export default RolesPage
