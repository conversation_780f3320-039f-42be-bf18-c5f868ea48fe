'use client'
import React, { useState } from 'react'
import {
  Box,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'

import { useAppSelector } from '@/app/redux'
import { IPermission, IRole } from '@/app/interfaces/roles'
import { CustomCheckBox } from '@/app/components/CheckBox'
import {
  RolesMoreMenu,
  ViewPermissions,
} from '@/app/dashboard/staff-users/roles/RolesMoreMenu'
import { CustomPagination } from '@/app/components/Table/Pagination'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import {
  CustomActiveChip,
  CustomChip,
  CustomErrorChip,
} from '@/app/components/Chip'
type Order = 'asc' | 'desc'
export const CustomTableCell = styled(TableCell)(() => ({
  color: '#667085',
}))

const tableHeadList = [
  { id: 'name', label: 'Role', alignRight: false },
  { id: 'custom', label: 'Is Custom', alignRight: false },
  { id: 'permissions', label: 'Rights', alignRight: false },
  { id: 'description', label: 'Description', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
interface ListRolesProps {
  onSelectedCountChange: (count: number) => void
  onExport: (selectedIds: string[]) => void
  page: number
  setPage: (page: number) => void
}
export const ListRoles = ({
  onSelectedCountChange,
  onExport,
  page,
  setPage,
}: ListRolesProps) => {
  const [selected, setSelected] = useState<readonly string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')

  // Retrieve the data from the store
  const data = useAppSelector((state) => state.roles.rolesList)
  const [filteredRoles] = useState<IRole[]>(data)
  const pageCount = useAppSelector((state) => state.roles.pageCount)
  /*************************start pagination handlers***************************/
  const handlePageClick = (newPage: number) => {
    setPage(newPage)
  }
  const handleForwardClick = () => {
    setPage(page + 1)
  }
  const handleBackClick = () => {
    setPage(page - 1)
  }
  /*************************end pagination handlers**************************/

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = data.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      onSelectedCountChange(newSelected.length)
      onExport(newSelected)
      return
    }
    setSelected([])
    onSelectedCountChange(0)
    onExport([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
    onSelectedCountChange(newSelected.length)
    onExport([...newSelected])
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }
  return (
    <Paper
      sx={{
        width: '100%',
        overflow: 'hidden',
        borderRadius: '4px',
        border: '1px solid var(--gray-200, #EAECF0)',
        background: '#FEFEFE',
        boxShadow:
          '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
      }}
    >
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="designations table"
          size="small"
        >
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={tableHeadList}
            showCheckbox={true}
            rowCount={data.length}
            numSelected={selected.length}
            onRequestSort={handleRequestSort}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {filteredRoles &&
              filteredRoles.map((row: IRole) => {
                const { id, name, permissions, custom, description } = row
                const isItemSelected = selected.indexOf(id) !== -1
                return (
                  <TableRow
                    hover
                    key={id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': id,
                        }}
                      />
                    </TableCell>
                    <TableCell component="th" scope="row" id={id}>
                      {sentenceCase(name)}
                    </TableCell>
                    <CustomTableCell>
                      {custom ? (
                        <CustomActiveChip label="Yes" />
                      ) : (
                        <CustomErrorChip label="No" />
                      )}
                    </CustomTableCell>
                    <CustomTableCell>
                      <Box
                        sx={{
                          display: 'flex',
                          gap: '4px',
                          padding: '16px 24px',
                        }}
                      >
                        {permissions
                          .slice(0, 2)
                          .map((permission: IPermission) => (
                            <CustomChip
                              key={permission.id}
                              label={permission.name.toLowerCase()}
                            />
                          ))}
                        {permissions.length > 2 && (
                          <ViewPermissions role={row} />
                        )}
                      </Box>
                    </CustomTableCell>
                    <TableCell>{description}</TableCell>
                    <TableCell>
                      <RolesMoreMenu role={row} />
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      <CustomPagination
        pageCount={pageCount}
        page={page}
        handlePageClick={handlePageClick}
        handleForwardClick={handleForwardClick}
        handleBackClick={handleBackClick}
      />
    </Paper>
  )
}
