import { Edit } from '@mui/icons-material'
import {
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import React, { FC } from 'react'

import { useAppSelector } from '@/app/redux'

interface profileDetailsProps {
  onClose: () => void
}

const ProfileDetailsPreview: FC<profileDetailsProps> = ({ onClose }) => {
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const extractFields = (field: string) => {
    const result = selectedApprovalRequest.diff.find(
      (val) => val.field === field
    )
    return result?.newValue as string
  }
  return (
    <Stack>
      <Stack direction="row" justifyContent={'space-between'}>
        <Typography>Profile Details</Typography>
        <Stack
          sx={{
            flexDirection: 'row',
            marginBottom: '5px',
          }}
        >
          <Button
            variant="outlined"
            endIcon={<Edit />}
            size="small"
            sx={{
              borderColor: '#E3E4E4',
              height: '30px',
            }}
            onClick={() => onClose()}
          >
            Close
          </Button>
        </Stack>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          border: '1px solid #EAECF0',
          borderRadius: '4px',
        }}
        elevation={0}
      >
        <Table sx={{}} aria-label="simple table">
          <TableHead
            sx={{
              background: '#F9FAFB',
            }}
          >
            <TableRow>
              <TableCell>Field</TableCell>
              <TableCell>Data</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableCell>First Name</TableCell>
            <TableCell>{extractFields('firstName')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Last Name</TableCell>
            <TableCell>{extractFields('lastName')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Gender</TableCell>
            <TableCell>
              {extractFields('gender') === 'F' ? 'Female' : 'Male'}
            </TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Phone number</TableCell>
            <TableCell>{extractFields('phoneNumber')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Email</TableCell>
            <TableCell>{extractFields('email')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>ID type</TableCell>
            <TableCell>{extractFields('idType')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>ID number</TableCell>
            <TableCell>{extractFields('idValue')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>CIF number</TableCell>
            <TableCell>{extractFields('cif')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Postal address</TableCell>
            <TableCell>{extractFields('postalAddress')}</TableCell>
          </TableBody>
          <TableBody>
            <TableCell>Physical address</TableCell>
            <TableCell>{extractFields('physicalAddress')}</TableCell>
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}

export default ProfileDetailsPreview
