import { Stack } from '@mui/material'
import React from 'react'

import { ReadOnlyTypography } from '@/app/components/ReadOnlyTypography'
import { handleDiff } from '@/app/utils/helpers'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'

export const ProfileUpdatePreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  return (
    <Stack>
      <ReadOnlyTypography
        name="changesMade"
        value={handleDiff(selectedApprovalRequest.diff)}
        id="changesMade"
        minRows={2}
        multiline
      />
    </Stack>
  )
}
