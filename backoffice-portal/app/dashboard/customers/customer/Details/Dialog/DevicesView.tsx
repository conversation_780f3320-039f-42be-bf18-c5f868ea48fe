import { IconButton, Stack, Typography } from '@mui/material'
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'

import { useAppSelector } from '@/app/redux'
import { DeviceIcons } from '@/app/components/SvgIcons/CustomerIcons'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'

export const DeviceChangesMadePreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  const { device } = useAppSelector((state) => state.customers)
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        gap: '10px',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        border: '1px solid #EAECF0',
        borderRadius: '4px',
        padding: '4%',
      }}
    >
      <IconButton
        sx={{
          backgroundColor: '#E7E8E9',
          borderRadius: '4px',
        }}
      >
        <DeviceIcons />
      </IconButton>
      <Stack direction="column" gap={'1%'}>
        <Typography variant="body1" color="text.primary">
          {device.deviceName}
        </Typography>
        <Stack
          sx={{
            flexDirection: 'row',
            alignContent: 'center',
            alignItems: 'center',
            gap: '3px',
          }}
        >
          <FiberManualRecordIcon
            sx={{
              fontSize: '8px',
              color: '#00000099',
            }}
          />
          <Typography variant="body2" color="text.primary2">
            {selectedApprovalRequest.makerCheckerType.type ===
            'ACTIVATE_PROFILEDEVICES'
              ? 'Activate Device'
              : 'Deactivate Device'}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}
