import { ArrowOutward } from '@mui/icons-material'
import { Button, List, ListItem, Stack, Typography } from '@mui/material'
import React from 'react'

import { ICustomerAccount } from '@/app/interfaces/customers'
import { AccountsSummaryIcon } from '@/app/components/SvgIcons/CustomerSummaryIcons'
import { CustomerSettingsIcon } from '@/app/components/SvgIcons/Users'
import { useAppSelector } from '@/app/redux'

export const CustomerCreateChangesMadePreview = ({
  openProfileTab,
  openAccountTab,
}: {
  openProfileTab: (val: boolean) => void
  openAccountTab: (account: ICustomerAccount) => void
}) => {
  const { customer, customerAccountsList } = useAppSelector(
    (state) => state.customers
  )
  return (
    <Stack>
      <Typography
        sx={{
          color: '#555C61',
          fontSize: '14px',
          marginBottom: '5px',
        }}
      >
        Changes made
      </Typography>
      <Stack
        sx={{
          py: '2%',
          px: '3%',
          border: '1px solid #E0E0E0',
          borderRadius: '4px',
          background: '#F9FAFB',
          flexDirection: 'row',
          justifyContent: 'space-between',
          marginBottom: '10px',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignContent: 'center',
            alignItems: 'flex-start',
            gap: '10px',
          }}
        >
          <CustomerSettingsIcon />
          <Stack>
            <Typography
              variant="subtitle2"
              color={'text.primary'}
              sx={{
                textWrap: 'noWrap',
              }}
            >
              {customer.firstName} {customer.lastName}
            </Typography>
            <List
              sx={{
                listStyleType: 'disc',
                paddingLeft: '15px',
                paddingTop: '0px',
              }}
            >
              <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                <Typography
                  variant="subtitle2"
                  color={'text.primary'}
                  sx={{
                    textWrap: 'noWrap',
                    color: '#555C61',
                    fontSize: '14px',
                  }}
                >
                  Create customer profile
                </Typography>
              </ListItem>
            </List>
          </Stack>
        </Stack>
        <Button
          variant="outlined"
          size="small"
          sx={{
            py: '2px',
            borderRadius: '8px',
            borderColor: '#E3E4E4',
          }}
          onClick={() => openProfileTab(true)}
          endIcon={<ArrowOutward />}
        >
          Profile Details
        </Button>
      </Stack>
      {customerAccountsList.map((account) => (
        <Stack
          key={account.accNumber}
          sx={{
            py: '2%',
            px: '3%',
            border: '1px solid #E0E0E0',
            borderRadius: '4px',
            background: '#F9FAFB',
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: '10px',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignContent: 'center',
              alignItems: 'flex-start',
              gap: '10px',
            }}
          >
            <AccountsSummaryIcon />
            <Stack>
              <Typography
                variant="subtitle2"
                color={'text.primary'}
                sx={{
                  textWrap: 'noWrap',
                }}
              >
                Account {account.accNumber}
              </Typography>
              <List
                sx={{
                  listStyleType: 'disc',
                  paddingLeft: '15px',
                  paddingTop: '0px',
                }}
              >
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add account
                  </Typography>
                </ListItem>
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add notifications
                  </Typography>
                </ListItem>
                <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                  <Typography
                    variant="subtitle2"
                    color={'text.primary'}
                    sx={{
                      textWrap: 'noWrap',
                      color: '#555C61',
                      fontSize: '14px',
                    }}
                  >
                    Add subscriptions
                  </Typography>
                </ListItem>
              </List>
            </Stack>
          </Stack>
          <Button
            variant="outlined"
            size="small"
            sx={{
              py: '2px',
              borderRadius: '8px',
              borderColor: '#E3E4E4',
            }}
            endIcon={<ArrowOutward />}
            onClick={() => openAccountTab(account)}
          >
            Account Details
          </Button>
        </Stack>
      ))}
    </Stack>
  )
}
