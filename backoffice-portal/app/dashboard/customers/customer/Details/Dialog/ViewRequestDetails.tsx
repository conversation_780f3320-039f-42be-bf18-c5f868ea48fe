import { Check, Close } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  <PERSON>con<PERSON>utton,
  MenuI<PERSON>,
  <PERSON>ack,
  <PERSON>Field,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'

import { AccountLinkingPreview } from '@/app/dashboard/customers/customer/Details/Dialog/AccountLinkingPreview'
import { CheckerRequestsApiHandler } from '@/app/dashboard/approval-requests/CheckerRequestsApiHandler'
import {
  formatTimestamp,
  handleDiff,
  isAccountLinkingApprovalRequest,
  useCustomRouter,
} from '@/app/utils/helpers'
import { DeviceChangesMadePreview } from '@/app/dashboard/customers/customer/Details/Dialog/DevicesView'
import { CustomerUpdatePreview } from '@/app/dashboard/customers/customer/Details/Dialog/CustomerUpdatePreview'
import { ProfileUpdatePreview } from '@/app/dashboard/customers/customer/Details/Dialog/ProfileUpdatePreview'
import { CustomerCreateChangesMadePreview } from '@/app/dashboard/customers/customer/Details/Dialog/CustomerCreatePreview'
import { ReadOnlyTypography } from '@/app/components/ReadOnlyTypography'
import { InfoIcon } from '@/app/components/SvgIcons/InfoIcon'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { setSelectedCustomerSummaryAccount } from '@/app/redux/reducers/customers'
import { ICustomerAccount } from '@/app/interfaces/customers'
import { setApprovalDrawerOpen } from '@/app/redux/reducers/ApprovalRequests'
import { setDrawer } from '@/app/redux/reducers/overlays'

import AccountDetailsPreview from './accountDetailsPreview'
import { AccountsEditPreview } from './accountsEditPreview'
import ProfileDetailsPreview from './profileDetailsPreview'
import { DeleteCustomerPreview } from './DeleteCustomerPreview'

const ViewRequestDetails = ({ hideButton }: { hideButton?: boolean }) => {
  // Redux state
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [open, setOpen] = useState<boolean>(false)
  const [openProfileDetails, setOpenProfileDetails] = useState<boolean>(false)
  const [openAccountDetails, setOpenAccountDetails] = useState<boolean>(false)
  const [checkerComments, setCheckerComments] = useState<string>('')
  const [commentsError, setCommentsError] = useState<boolean>(false)

  const { selectedApprovalRequest, approvalDrawerOpen } = useAppSelector(
    (state) => state.approvalRequests
  )

  const handleClose = (e: unknown, action: string) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
    dispatch(setApprovalDrawerOpen(false))
  }

  const handleReject = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
      checkerComments
    )
    handleClose(null, 'close')
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const handleApprove = async () => {
    if (!checkerComments) {
      setCommentsError(true)
      return
    }

    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedApprovalRequest.makerCheckerType.type}`,
      checkerComments
    )
    handleClose(null, 'close')
    dispatch(
      setDrawer({
        open: false,
        drawerChildren: null,
        header: '',
      })
    )
  }

  const dialogSize = '30%'
  const expandedDialogSize = '70%'

  const openAccountTab = (account: ICustomerAccount) => {
    setOpenAccountDetails(true)
    setOpenProfileDetails(false)
    dispatch(setSelectedCustomerSummaryAccount(account))
  }
  const openProfileTab = () => {
    setOpenAccountDetails(false)
    setOpenProfileDetails(true)
  }
  useEffect(() => {
    approvalDrawerOpen ? setOpen(true) : setOpen(false)
  }, [approvalDrawerOpen])
  return (
    <>
      {!!hideButton === false && (
        <MenuItem
          onClick={() => setOpen(!open)}
          sx={{
            backgroundColor: '#FFFFFF',
            border: '1px solid  #AAADB0',
            borderRadius: '4px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            height: '34px',
          }}
        >
          View Approval Request Details
        </MenuItem>
      )}

      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width:
              openProfileDetails || openAccountDetails
                ? expandedDialogSize
                : dialogSize,
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width:
              openProfileDetails || openAccountDetails
                ? expandedDialogSize
                : dialogSize,
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            px: '1.5vw',
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              fontSize: '20px',
            }}
            color="text.primary"
          >
            Approval request details
          </Typography>
          <IconButton onClick={() => handleClose(null, 'close')}>
            <Close />
          </IconButton>
        </Stack>
        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: '1%',
            justifyContent: 'space-between',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'column',
              gap: '2vh',
              width: openProfileDetails || openAccountDetails ? '48%' : '100%',
            }}
          >
            <ReadOnlyTypography
              label="Approval request type"
              value={selectedApprovalRequest.makerCheckerType?.name}
            />
            <Typography>Changes made</Typography>
            {/** switch to handle changes made on different maker checker processes**/}
            {(() => {
              switch (selectedApprovalRequest.makerCheckerType?.type) {
                case 'ACTIVATE_PROFILEDEVICES':
                  return (
                    <DeviceChangesMadePreview
                      selectedApprovalRequest={selectedApprovalRequest}
                    />
                  )
                case 'DEACTIVATE_PROFILEDEVICES':
                  return (
                    <DeviceChangesMadePreview
                      selectedApprovalRequest={selectedApprovalRequest}
                    />
                  )
                case 'CREATE_CUSTOMERS':
                  return (
                    <CustomerCreateChangesMadePreview
                      openAccountTab={openAccountTab}
                      openProfileTab={openProfileTab}
                    />
                  )
                case 'UPDATE_CUSTOMERS':
                  if (
                    isAccountLinkingApprovalRequest(
                      selectedApprovalRequest.entity || ''
                    )
                  ) {
                    return (
                      <AccountLinkingPreview
                        selectedApprovalRequest={selectedApprovalRequest}
                      />
                    )
                  } else {
                    return (
                      <CustomerUpdatePreview
                        selectedApprovalRequest={selectedApprovalRequest}
                      />
                    )
                  }
                case 'CREATE_ACCOUNTS':
                  return (
                    <AccountsEditPreview
                      selectedApprovalRequest={selectedApprovalRequest}
                      setOpenAccountDetails={(val) =>
                        setOpenAccountDetails(val ? true : false)
                      }
                    />
                  )
                case 'RESTRICT_ACCOUNTS':
                  return (
                    <AccountsEditPreview
                      selectedApprovalRequest={selectedApprovalRequest}
                      setOpenAccountDetails={(val) =>
                        setOpenAccountDetails(val ? true : false)
                      }
                    />
                  )
                case 'DELETE_ACCOUNTS':
                  return (
                    <AccountsEditPreview
                      selectedApprovalRequest={selectedApprovalRequest}
                      setOpenAccountDetails={(val) =>
                        setOpenAccountDetails(val ? true : false)
                      }
                    />
                  )
                case 'UPDATE_PROFILES':
                  return (
                    <ProfileUpdatePreview
                      selectedApprovalRequest={selectedApprovalRequest}
                    />
                  )
                case 'DELETE_CUSTOMERS':
                  return (
                    <DeleteCustomerPreview
                      selectedApprovalRequest={selectedApprovalRequest}
                    />
                  )
                default:
                  return (
                    <Stack>
                      <ReadOnlyTypography
                        name="changesMade"
                        value={handleDiff(selectedApprovalRequest.diff) || ''}
                        id="changesMade"
                        minRows={2}
                        multiline
                      />
                    </Stack>
                  )
              }
            })()}
            <ReadOnlyTypography
              label="Maker"
              value={selectedApprovalRequest.maker}
            />
            <ReadOnlyTypography
              label="Maker Timestamp"
              value={formatTimestamp(selectedApprovalRequest.dateCreated)}
            />
            <ReadOnlyTypography
              label="Maker Comments"
              value={selectedApprovalRequest.makerComments}
            />
            <TextField
              label={'Checker Comments'}
              value={checkerComments}
              onChange={(e) => {
                setCheckerComments(e.target.value)
                // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                e.target.value.length > 0
                  ? setCommentsError(false)
                  : setCommentsError(true)
              }}
              error={commentsError}
              helperText={commentsError ? 'Please enter comments' : ''}
              multiline
              rows={4}
            />
            <Stack direction="row" gap={'2%'}>
              <Button
                variant="outlined"
                fullWidth
                sx={{
                  maxHeight: '34px',
                  background: '#E3E4E4',
                  border: '1px solid #AAADB0',
                  boxShadow: ' 0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
                onClick={handleReject}
              >
                Reject
              </Button>
              <Button
                variant="contained"
                fullWidth
                sx={{
                  maxHeight: '34px',
                }}
                onClick={handleApprove}
              >
                Approve
                <Check />
              </Button>
            </Stack>
            <Stack
              direction="row"
              gap={'2%'}
              sx={{
                alignItems: 'center',
                alignContent: 'center',
              }}
            >
              <InfoIcon />
              <Typography variant="body2">
                Please review and approve or reject based on your judgement.
              </Typography>
            </Stack>
          </Stack>
          {(openProfileDetails || openAccountDetails) && (
            <Divider orientation="vertical" variant="fullWidth" />
          )}
          <Stack
            sx={{
              display:
                openProfileDetails || openAccountDetails ? 'flex' : 'none',
              width: '48%',
            }}
          >
            {openAccountDetails ? (
              <AccountDetailsPreview
                onClose={() => setOpenAccountDetails(false)}
              />
            ) : openProfileDetails ? (
              <ProfileDetailsPreview
                onClose={() => setOpenProfileDetails(false)}
              />
            ) : null}
          </Stack>
        </DialogContent>
      </Drawer>
    </>
  )
}

export default ViewRequestDetails
