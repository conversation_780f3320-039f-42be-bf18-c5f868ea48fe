import { Edit } from '@mui/icons-material'
import {
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import React, { FC } from 'react'

import { useAppSelector } from '@/app/redux'
import { accountType } from '@/app/redux/actions/customers'

import { CustomChip } from '../../../customers/Create/Summary'

interface accountDetailsProps {
  onClose: () => void
}

const AccountDetailsPreview: FC<accountDetailsProps> = ({ onClose }) => {
  const summaryCustomerAccount = useAppSelector(
    (state) => state.customers.selectedCustomerSummaryAccount
  )
  const customerProfileAccount = useAppSelector(
    (state) => state.customers.customerProfileAccount
  )
  const { bankBranches } = useAppSelector((state) => state.loans)
  const bankBranch = (code: string) => {
    const branch = bankBranches.find((branch) => branch.branchCode === code)
    return branch?.branchName ? branch.branchName : 'No branch'
  }
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const extractFields = (field: string) => {
    const result = selectedApprovalRequest.diff.find(
      (val) => val.field === field
    )
    return result?.newValue as string
  }
  return (
    <Stack direction="column" gap="2vh">
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Accounts Details</Typography>
          <Stack
            sx={{
              flexDirection: 'row',
              marginBottom: '5px',
            }}
          >
            <Button
              variant="outlined"
              endIcon={<Edit />}
              size="small"
              sx={{
                borderColor: '#E3E4E4',
                height: '30px',
              }}
              onClick={() => onClose()}
            >
              Close
            </Button>
          </Stack>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Field</TableCell>
                <TableCell>Data</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Account name</TableCell>
              <TableCell>
                {summaryCustomerAccount?.accClassDesc ||
                  customerProfileAccount?.fullName}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Account number</TableCell>
              <TableCell>
                {summaryCustomerAccount.accNumber ||
                  customerProfileAccount?.accountNo}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Type</TableCell>
              <TableCell>
                {customerProfileAccount?.mandate ||
                  accountType(summaryCustomerAccount?.customerType)}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Tarrif</TableCell>
              <TableCell>
                {summaryCustomerAccount?.accCurrency ||
                  customerProfileAccount?.currency}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Branch</TableCell>
              <TableCell>
                {summaryCustomerAccount?.accBranchCode
                  ? bankBranch(summaryCustomerAccount?.accBranchCode)
                  : bankBranch(customerProfileAccount?.branchCode)}
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Phone number</TableCell>
              <TableCell>
                {customerProfileAccount?.profile?.phoneNumber ||
                  extractFields('phoneNumber')}
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Subscriptions (E-statements)</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Email</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Daily</TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.email ||
                    extractFields('email')
                  }
                />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Loan Installment Due</TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.email ||
                    extractFields('email')
                  }
                />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Notifications</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Phone Numbers</TableCell>
              </TableRow>
              ntre
            </TableHead>
            <TableBody>
              <TableCell>Credit & Debit</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
                <CustomChip prop={'Email'} />
              </TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.phoneNumber ||
                    extractFields('phoneNumber')
                  }
                />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Loan Installment Due</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.phoneNumber ||
                    extractFields('phoneNumber')
                  }
                />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      <>
        <Stack direction="row" justifyContent={'space-between'}>
          <Typography>Alerts</Typography>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Channels</TableCell>
                <TableCell>Phone Number</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableCell>Daily</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.phoneNumber ||
                    extractFields('phoneNumber')
                  }
                />
              </TableCell>
            </TableBody>
            <TableBody>
              <TableCell>Weekly</TableCell>
              <TableCell>
                <CustomChip prop={'SMS'} />
              </TableCell>
              <TableCell>
                <CustomChip
                  prop={
                    customerProfileAccount?.profile?.phoneNumber ||
                    extractFields('phoneNumber')
                  }
                />
              </TableCell>
            </TableBody>
          </Table>
        </TableContainer>
      </>
    </Stack>
  )
}

export default AccountDetailsPreview
