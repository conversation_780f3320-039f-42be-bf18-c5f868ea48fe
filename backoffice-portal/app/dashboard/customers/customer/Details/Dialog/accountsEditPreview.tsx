import { But<PERSON>, <PERSON>, ListItem, Stack, Typography } from '@mui/material'
import React from 'react'
import CallMadeIcon from '@mui/icons-material/CallMade'

import { AccountsSummaryIcon } from '@/app/components/SvgIcons/CustomerSummaryIcons'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { setCustomerProfileAccount } from '@/app/redux/reducers/customers'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { getCustomerAccountByAccountNo } from '@/app/redux/actions/customers'
import { getBankBranches } from '@/app/redux/actions/loans'

export const AccountsEditPreview = ({
  selectedApprovalRequest,
  setOpenAccountDetails,
}: {
  selectedApprovalRequest: IApprovalRequest
  setOpenAccountDetails: (val: boolean) => void
}) => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  const accountNo = JSON.parse(selectedApprovalRequest?.entity || '')?.accountNo
    ? JSON.parse(selectedApprovalRequest?.entity || '')?.accountNo
    : JSON.parse(selectedApprovalRequest?.entity || '')?.accounts[0]?.accNumber

  return (
    <Stack>
      <Stack
        sx={{
          py: '2%',
          px: '5%',
          border: '1px solid #E0E0E0',
          borderRadius: '4px',
          flexDirection: 'row',
          marginBottom: '10px',
          alignItems: 'center',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            gap: '10px',
          }}
        >
          <Stack sx={{ marginTop: '2%', zoom: '120%', marginRight: '2%' }}>
            <AccountsSummaryIcon />
          </Stack>
          <Stack>
            <Typography
              variant="subtitle2"
              color={'text.primary'}
              sx={{
                textWrap: 'noWrap',
              }}
            >
              Account {accountNo}
            </Typography>
            <List
              sx={{
                listStyleType: 'disc',
                paddingLeft: '15px',
                paddingTop: '0px',
              }}
            >
              <ListItem sx={{ display: 'list-item', padding: '0px' }}>
                <Typography
                  variant="subtitle2"
                  color={'text.primary'}
                  sx={{
                    textWrap: 'noWrap',
                    color: '#555C61',
                    fontSize: '14px',
                  }}
                >
                  {selectedApprovalRequest?.makerCheckerType?.name}
                </Typography>
              </ListItem>
            </List>
          </Stack>
        </Stack>
        <Stack sx={{ marginLeft: 'auto' }}>
          <Button
            variant="outlined"
            onClick={async () => {
              const accountDetails = await getCustomerAccountByAccountNo(
                customer.id || '',
                accountNo || '',
                dispatch
              )
              getBankBranches(dispatch)
              dispatch(setCustomerProfileAccount(accountDetails))
              setOpenAccountDetails(true)
            }}
            sx={{
              border: '1px solid #E3E4E4',
              fontSize: '15px',
              fontWeight: '600',
              padding: '0px 10px',
            }}
            endIcon={<CallMadeIcon style={{ fontSize: 15 }} />}
          >
            See details
          </Button>
        </Stack>
      </Stack>
    </Stack>
  )
}
