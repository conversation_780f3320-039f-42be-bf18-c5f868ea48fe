import { Stack, Typography } from '@mui/material'
import React from 'react'

import { handleDiff } from '@/app/utils/helpers'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { AccountsSummaryIcon } from '@/app/components/SvgIcons/CustomerSummaryIcons'
import { useAppSelector } from '@/app/redux'

export const CustomerUpdatePreview = ({
  selectedApprovalRequest,
}: {
  selectedApprovalRequest: IApprovalRequest
}) => {
  const { customer } = useAppSelector((state) => state.customers)
  return (
    <Stack>
      <Stack
        sx={{
          py: '2%',
          px: '5%',
          border: '1px solid #E0E0E0',
          borderRadius: '4px',
          flexDirection: 'row',
          marginBottom: '10px',
          alignItems: 'center',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'flex-start',
            gap: '10px',
          }}
        >
          <Stack sx={{ marginTop: '2%', zoom: '120%', marginRight: '2%' }}>
            <AccountsSummaryIcon />
          </Stack>
          <Stack>
            <Typography
              variant="subtitle2"
              color={'text.primary'}
              sx={{
                textWrap: 'noWrap',
              }}
            >
              {`${customer?.firstName} ${customer?.lastName}`}
            </Typography>
            <Typography
              variant="subtitle2"
              color={'text.primary'}
              sx={{
                color: '#555C61',
                fontSize: '14px',
              }}
            >
              {handleDiff(selectedApprovalRequest.diff)}
            </Typography>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}
