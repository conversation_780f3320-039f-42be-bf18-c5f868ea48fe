import { CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material'
import React, { FC } from 'react'

interface ConfirmCancelProps {
  open: boolean
  onClose: () => void
  onConfirmCancel: () => void
  onConfirmSubmit: () => void
}

const ConfirmCancelSave: FC<ConfirmCancelProps> = ({
  open,
  onClose,
  onConfirmCancel,
  onConfirmSubmit,
}) => {
  return (
    <Dialog open={open} onClose={onClose} sx={{}}>
      <Box sx={{ width: '383px' }}>
        <DialogTitle
          sx={{
            borderBottom: '1px solid #CBD5E1',
            padding: '16px 20px 8px 24px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#F9FAFB',
          }}
        >
          <Typography sx={{ color: '#000A12', fontWeight: 600 }}>
            Discard changes?
          </Typography>
          <IconButton
            onClick={onClose}
            sx={{
              width: '36px',
              height: '36px',
              backgroundColor: ' #F1F5F9',
              border: '1px solid #CBD5E1)',
              borderRadius: '50%',
            }}
          >
            <CloseRounded />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
            padding: '0px 20px 20px 20px',
          }}
        >
          <DialogContentText
            sx={{
              textAlign: 'center',
            }}
          >
            You have unsaved changes, are you sure you want to discard these
            changes?
          </DialogContentText>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: '20px',
            }}
          >
            {/* footeer */}
            <Button
              variant="outlined"
              sx={{
                backgroundColor: '#FFFFFF',
                border: '1.5px solid  #AAADB0',
                borderRadius: '4px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                color: '#555C61',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                textAlign: 'center',
                width: '153px',
                height: '40px',
                textWrap: 'nowrap',
              }}
              onClick={onConfirmCancel}
            >
              {"Yes, I'm sure."}
            </Button>
            <Button
              variant="contained"
              sx={{
                border: '1.5px solid  #AAADB0',
                borderRadius: '4px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                fontSize: '16px',
                fontStyle: 'normal',
                fontWeight: '500',
                lineHeight: '24px',
                textAlign: 'center',
                width: '153px',
                height: '40px',
                textWrap: 'nowrap',
              }}
              onClick={onConfirmSubmit}
            >
              Save changes
            </Button>
          </Box>
        </DialogContent>
      </Box>
    </Dialog>
  )
}

export default ConfirmCancelSave
