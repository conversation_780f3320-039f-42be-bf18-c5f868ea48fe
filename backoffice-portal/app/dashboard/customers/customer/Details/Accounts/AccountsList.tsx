import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React from 'react'

import { ICustomerProfileAccount } from '@/app/interfaces/customers'
import { getCustomerAccountByAccountNo } from '@/app/redux/actions/customers'
import { setIsViewAccountOpen } from '@/app/redux/reducers/customers'
import { CustomerStatusChip } from '@/app/components/Chip'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { AccountsMoreMenu } from '@/app/dashboard/customers/customer/Details/Accounts/AccountsMoreMenu'
import { IHeadCell } from '@/app/interfaces/shared'
import { useAppDispatch, useAppSelector } from '@/app/redux'

const headerData: IHeadCell[] = [
  {
    id: 'name',
    label: 'Account Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'shortName',
    label: 'Account Alias',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'accNumber',
    label: 'Account Number',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'currency',
    label: 'Currency',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'tariff',
    label: 'Tariff',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'type',
    label: 'Type',
    alignCenter: false,
    alignRight: false,
  },

  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

export const AccountsList = () => {
  const { customerLinkedAccountsList, customer } = useAppSelector(
    (state) => state.customers
  )
  const dispatch = useAppDispatch()
  const handleRowClick = async (account: ICustomerProfileAccount) => {
    const accountDetails = await getCustomerAccountByAccountNo(
      customer.id ? customer.id : '',
      account.accountNo,
      dispatch
    )
    dispatch(setIsViewAccountOpen(!!accountDetails?.profile?.phoneNumber))
  }
  return (
    <Stack direction="column">
      <TableContainer
        component={Paper}
        sx={{
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        }}
      >
        <Table>
          <CustomTableHeader
            order={'desc'}
            orderBy={'id'}
            headLabel={headerData}
            showCheckbox={false}
            rowCount={2}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={() => {}}
          />
          <TableBody>
            {customerLinkedAccountsList.map((row) => (
              <TableRow key={row.accountNo}>
                <TableCell
                  onClick={() => handleRowClick(row)}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  {row?.fullName || ''}
                </TableCell>
                <TableCell
                  onClick={() => handleRowClick(row)}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  {row.shortName}
                </TableCell>
                <TableCell
                  onClick={() => handleRowClick(row)}
                  sx={{
                    cursor: 'pointer',
                  }}
                >
                  {row.accountNo}
                </TableCell>
                <TableCell>
                  <CustomerStatusChip label={row.status} />
                </TableCell>
                <TableCell>{row.currency}</TableCell>
                <TableCell>{row.tariffName}</TableCell>
                <TableCell>{row.accountType}</TableCell>
                <TableCell>
                  <AccountsMoreMenu account={row} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  )
}
