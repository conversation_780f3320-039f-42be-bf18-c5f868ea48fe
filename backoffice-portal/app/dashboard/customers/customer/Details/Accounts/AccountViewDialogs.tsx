import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { Button, Menu, MenuItem } from '@mui/material'
import React, { useState } from 'react'

import { useAppSelector } from '@/app/redux'

export const AddNotificationDialog = ({}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const { customer } = useAppSelector((state) => state.customers)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <Button
        variant="outlined"
        startIcon={<AddOutlinedIcon />}
        size="small"
        onClick={handleClick}
        sx={{
          height: '35px',
          border: '1px solid #E3E4E4',
          textWrap: 'noWrap',
          background: '#F8F9FC',
        }}
        disabled={customer.isBlocked}
      >
        Add Notification
      </Button>
      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <MenuItem>Credit & Debit</MenuItem>
        <MenuItem>Daily balance</MenuItem>
        <MenuItem>Weekly balance</MenuItem>
        <MenuItem>Monthly balance</MenuItem>
        <MenuItem>Loan Installment Due</MenuItem>
        <MenuItem>Fixed deposit Maturity</MenuItem>
        <MenuItem>Overdrawn Account</MenuItem>
      </Menu>
    </>
  )
}
