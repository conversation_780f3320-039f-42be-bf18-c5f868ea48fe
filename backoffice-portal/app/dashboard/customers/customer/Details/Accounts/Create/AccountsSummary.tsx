import { ArrowOutward, EditOutlined } from '@mui/icons-material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import {
  Button,
  Divider,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import { FormikProps } from 'formik'
import React, { useState } from 'react'

import { HasAccessToRights } from '@/app/utils/AccessControlHelper'
import {
  ICustomerAccount,
  ICustomerAccountLink,
} from '@/app/interfaces/customers'
import { LoadingButton } from '@/app/components/Loading/LoadingButton'
import { useAppSelector } from '@/app/redux'
import { AccountsSummaryIcon } from '@/app/components/SvgIcons/CustomerSummaryIcons'
export type IAccountsLinkProps = {
  formik: FormikProps<ICustomerAccountLink>
  setStep: (step: string) => void
}
export const AccountsSummary = ({ formik, setStep }: IAccountsLinkProps) => {
  const [accountInView, setAccountInView] = useState<ICustomerAccount>(
    formik.values.accounts[0]
      ? formik.values.accounts[0]
      : ({} as ICustomerAccount)
  )
  const handleAccountView = (account: ICustomerAccount) => {
    setAccountInView(account)
  }
  const { isLoadingLinkAccounts } = useAppSelector((state) => state.customers)
  return (
    <Stack
      sx={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      direction="column"
    >
      <Stack
        sx={{
          alignContent: 'center',
          alignItems: 'center',
          py: '2%',
          gap: '1vh',
          width: '60%',
        }}
      >
        <Typography variant="body1" color="primary.main">
          STEP 2 OF 2
        </Typography>
        <Typography variant="h6">Summary</Typography>
        <Typography
          variant="label1"
          textAlign="center"
          color="primary.primary3"
        >
          Review, edit if need be then submit to checker.
        </Typography>
      </Stack>
      <Stack
        sx={{
          width: '100%',
          px: '3%',
        }}
        direction="row"
        gap={'2vh'}
      >
        <Stack
          sx={{
            width: '40%',
            gap: '2vh',
          }}
        >
          {formik.values.accounts.map((account: ICustomerAccount) => (
            <Stack
              key={account.accNumber}
              sx={{
                py: '3%',
                px: '3%',
                border: '1px solid #E0E0E0',
                borderRadius: '4px',
                background:
                  accountInView.accNumber === account.accNumber
                    ? '#F9FAFB'
                    : '#FFFFFF',
                flexDirection: 'row',
                justifyContent: 'space-between',
              }}
            >
              <Stack direction="column">
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignContent: 'center',
                    alignItems: 'flex-start',
                    gap: '10px',
                  }}
                >
                  <AccountsSummaryIcon />
                  <Stack>
                    <Typography
                      variant="subtitle2"
                      color={'text.primary'}
                      sx={{
                        textWrap: 'noWrap',
                      }}
                    >
                      Account {account.accNumber}
                    </Typography>
                    {/* //TODO: Uncomment the following lines on completion of the account dependency apis
                     */}
                    {/*<Typography variant="label2" color={'primary.primary4'}>*/}
                    {/*  1. Notifications*/}
                    {/*</Typography>*/}
                    {/*<Typography variant="label2" color={'primary.primary4'}>*/}
                    {/*  2. Balance Alerts*/}
                    {/*</Typography>*/}
                    {/*<Typography variant="label2" color={'primary.primary4'}>*/}
                    {/*  3. E-statements*/}
                    {/*</Typography>*/}
                  </Stack>
                </Stack>
              </Stack>

              <Button
                variant="outlined"
                size="small"
                sx={{
                  py: '2px',
                  borderRadius: '8px',
                  borderColor: '#E3E4E4',
                }}
                endIcon={<ArrowOutward />}
                onClick={() => handleAccountView(account)}
              >
                Account Details
              </Button>
            </Stack>
          ))}
        </Stack>
        <Divider
          orientation="vertical"
          variant="middle"
          sx={{
            height: 'auto',
          }}
        />
        <Stack
          sx={{
            width: '60%',
          }}
        >
          <AccountsView accountInView={accountInView} setStep={setStep} />
        </Stack>
      </Stack>
      <Stack
        sx={{ width: '70%', py: '3vh' }}
        direction="row"
        justifyContent="space-between"
        gap="4%"
      >
        <Button
          variant="outlined"
          fullWidth
          onClick={() => setStep('Link and setup accounts')}
          startIcon={<ArrowBackIcon />}
        >
          Back
        </Button>
        {isLoadingLinkAccounts ? (
          <LoadingButton />
        ) : (
          <Button
            variant="contained"
            fullWidth
            type={'submit'}
            endIcon={<ArrowForwardIcon />}
          >
            {HasAccessToRights(['SUPER_UPDATE_CUSTOMERS'])
              ? 'Submit'
              : 'Submit to Checker'}
          </Button>
        )}
      </Stack>
    </Stack>
  )
}
export const AccountsView = ({
  accountInView,
  setStep,
}: {
  accountInView: ICustomerAccount
  setStep: (step: string) => void
}) => {
  const { customer } = useAppSelector((state) => state.customers)
  return (
    <Stack direction="column" gap="2vh">
      <>
        <Stack direction="row" justifyContent="space-between">
          <Typography>Accounts Details</Typography>
          <Stack
            sx={{
              flexDirection: 'row',
              marginBottom: '5px',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              variant="outlined"
              endIcon={<EditOutlined />}
              sx={{
                borderColor: '#E3E4E4',
                height: '30px',
                alignContent: 'center',
                px: '10px',
                fontWeight: 500,
                fontSize: '14px',
              }}
              onClick={() => setStep('Link accounts')}
            >
              Edit
            </Button>
            {/*<Button*/}
            {/*  endIcon={<CloseOutlined />}*/}
            {/*  size="small"*/}
            {/*  sx={{*/}
            {/*    color: 'primary.main',*/}
            {/*    height: '30px',*/}
            {/*    px: '10px',*/}
            {/*  }}*/}
            {/*>*/}
            {/*  Close*/}
            {/*</Button>*/}
          </Stack>
        </Stack>
        <TableContainer
          component={Paper}
          sx={{
            border: '1px solid #EAECF0',
            borderRadius: '4px',
          }}
          elevation={0}
        >
          <Table sx={{}} aria-label="simple table">
            <TableHead
              sx={{
                background: '#F9FAFB',
              }}
            >
              <TableRow>
                <TableCell>Field</TableCell>
                <TableCell>Data</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <TableRow>
                <TableCell>Account Name</TableCell>
                <TableCell>{`${customer.firstName} ${customer.lastName}`}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Account Number</TableCell>
                <TableCell>{accountInView.accNumber}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>
                  {accountInView.customerType === 'I' ? 'Individual' : 'Joint'}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Tariff</TableCell>
                <TableCell>{accountInView.accCurrency}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Branch</TableCell>
                <TableCell>{accountInView.accBranchCode}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Created On</TableCell>
                <TableCell>{accountInView.accOpenDate}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Phone Number</TableCell>
                <TableCell>{customer.phoneNumber}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </>
      {/* //TODO: Uncomment when apis are integrated*/}
      {/*<>*/}
      {/*  <Stack direction="row" justifyContent={'space-between'}>*/}
      {/*    <Typography>Subscriptions (E-statements)</Typography>*/}
      {/*  </Stack>*/}
      {/*  <TableContainer*/}
      {/*    component={Paper}*/}
      {/*    sx={{*/}
      {/*      border: '1px solid #EAECF0',*/}
      {/*      borderRadius: '4px',*/}
      {/*    }}*/}
      {/*    elevation={0}*/}
      {/*  >*/}
      {/*    <Table sx={{}} aria-label="simple table">*/}
      {/*      <TableHead*/}
      {/*        sx={{*/}
      {/*          background: '#F9FAFB',*/}
      {/*        }}*/}
      {/*      >*/}
      {/*        <TableRow>*/}
      {/*          <TableCell>Type</TableCell>*/}
      {/*          <TableCell>Email</TableCell>*/}
      {/*        </TableRow>*/}
      {/*      </TableHead>*/}
      {/*      <TableBody>*/}
      {/*        <TableCell>Account Number</TableCell>*/}
      {/*        <TableCell>1234567</TableCell>*/}
      {/*      </TableBody>*/}
      {/*    </Table>*/}
      {/*  </TableContainer>*/}
      {/*</>*/}
      {/*<>*/}
      {/*  <Stack direction="row" justifyContent={'space-between'}>*/}
      {/*    <Typography>Notifications</Typography>*/}
      {/*  </Stack>*/}
      {/*  <TableContainer*/}
      {/*    component={Paper}*/}
      {/*    sx={{*/}
      {/*      border: '1px solid #EAECF0',*/}
      {/*      borderRadius: '4px',*/}
      {/*    }}*/}
      {/*    elevation={0}*/}
      {/*  >*/}
      {/*    <Table sx={{}} aria-label="simple table">*/}
      {/*      <TableHead*/}
      {/*        sx={{*/}
      {/*          background: '#F9FAFB',*/}
      {/*        }}*/}
      {/*      >*/}
      {/*        <TableRow>*/}
      {/*          <TableCell>Type</TableCell>*/}
      {/*          <TableCell>Channels</TableCell>*/}
      {/*          <TableCell>Phone Numbers</TableCell>*/}
      {/*        </TableRow>*/}
      {/*      </TableHead>*/}
      {/*      <TableBody>*/}
      {/*        <TableCell>Account Number</TableCell>*/}
      {/*        <TableCell>Email</TableCell>*/}
      {/*        <TableCell>********</TableCell>*/}
      {/*      </TableBody>*/}
      {/*    </Table>*/}
      {/*  </TableContainer>*/}
      {/*</>*/}
      {/*<>*/}
      {/*  <Stack direction="row" justifyContent={'space-between'}>*/}
      {/*    <Typography>Alerts</Typography>*/}
      {/*  </Stack>*/}
      {/*  <TableContainer*/}
      {/*    component={Paper}*/}
      {/*    sx={{*/}
      {/*      border: '1px solid #EAECF0',*/}
      {/*      borderRadius: '4px',*/}
      {/*    }}*/}
      {/*    elevation={0}*/}
      {/*  >*/}
      {/*    <Table sx={{}} aria-label="simple table">*/}
      {/*      <TableHead*/}
      {/*        sx={{*/}
      {/*          background: '#F9FAFB',*/}
      {/*        }}*/}
      {/*      >*/}
      {/*        <TableRow>*/}
      {/*          <TableCell>Type</TableCell>*/}
      {/*          <TableCell>Channels</TableCell>*/}
      {/*          <TableCell>Phone Number</TableCell>*/}
      {/*        </TableRow>*/}
      {/*      </TableHead>*/}
      {/*      <TableBody>*/}
      {/*        <TableCell>Daily</TableCell>*/}
      {/*        <TableCell>Email</TableCell>*/}
      {/*        <TableCell>1234567</TableCell>*/}
      {/*      </TableBody>*/}
      {/*    </Table>*/}
      {/*  </TableContainer>*/}
      {/*</>*/}
    </Stack>
  )
}
