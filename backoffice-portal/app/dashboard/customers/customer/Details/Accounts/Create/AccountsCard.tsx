import {
  <PERSON><PERSON>,
  <PERSON>box,
  Divider,
  Radio,
  Stack,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
} from '@mui/material'
import React, { useEffect, useState } from 'react'

import { ExpandableView } from '@/app/dashboard/customers/customers/Create/Accounts'
import { CustomIconButton } from '@/app/components/CustomIconButton'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  BalanceAlertIcon,
  EstatementIcon,
  LeftIcon,
  NotificationsIcon,
  RightIcon,
  TariffCheckedIcon,
  TariffIcon,
} from '@/app/components/SvgIcons/AccountIcons'
import { getAccountTariffs } from '@/app/redux/actions/customers'
import { CheckBoxIcon } from '@/app/components/SvgIcons/CheckBoxIcons'
import { Expandless } from '@/app/components/SvgIcons/Expandless'
import { Expandmore } from '@/app/components/SvgIcons/Expandmore'
import { ICustomerAccount } from '@/app/interfaces/customers'
import { accountType } from '@/app/redux/actions/customers'
import { CheckIcon, IconClose } from '@/app/components/SvgIcons/comment'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'

export interface IAccountLinkCardProps {
  account: ICustomerAccount
  handleLinkedStatus: (index: string) => void
  handleExpand: (accountNumber: string, type: ExpandableView) => void
  handleExpandCard: (accountNumber: string) => void
  onUpdateAccount: (updatedAcc: ICustomerAccount) => void
  expanded: string[]
  expandedCard: string[]
  selectedAccounts: ICustomerAccount[]
  expandedTariffs: string[]
  expandedNotifications: string[]
  expandedStatements: string[]
  expandedBalanceAlerts: string[]
  key: string
}
export const AccountsCard = ({
  account,
  handleLinkedStatus,
  handleExpand,
  handleExpandCard,
  onUpdateAccount,
  selectedAccounts,
  expandedTariffs,
  expandedNotifications,
  expandedBalanceAlerts,
  expandedStatements,
  expandedCard,
  key,
}: IAccountLinkCardProps) => {
  const [isTariffSelectedAndSaved, setIsTariffSelectedAndSaved] =
    useState(false)
  const getStackStyles = (
    accountNumber: string,
    expandedStates: { [key: string]: string[] }
  ) => {
    const { tariffs, notifications, statements, balanceAlerts } = expandedStates

    const isExpandedHorizontally = [
      tariffs,
      notifications,
      statements,
      balanceAlerts,
    ].some((expandedList) => expandedList.includes(accountNumber))

    return {
      flexDirection: isExpandedHorizontally ? 'row' : 'column',
      border: isExpandedHorizontally ? '0' : '1px solid #D0D5DD',
    }
  }

  const expandedStates = {
    tariffs: expandedTariffs,
    notifications: expandedNotifications,
    statements: expandedStatements,
    balanceAlerts: expandedBalanceAlerts,
  }

  const stackStyles = getStackStyles(account.accNumber, expandedStates)

  return (
    <Stack
      key={key}
      sx={{
        // border: expandedTariffs.includes(account.accNumber)
        //   ? 0
        //   : '1px solid #D0D5DD',
        ...stackStyles,

        borderRadius: '4px',
        py: '1%',
        gap: '2.5rem',
        padding: '10px',
        // width: expandedTariffs.includes(account.accNumber) ? '50%' : '100%',
        width: '100%',
        // flexDirection: expandedTariffs.includes(account.accNumber)
        //   ? 'row'
        //   : 'column',
      }}
    >
      <Stack
        sx={{
          border: expandedTariffs.includes(account.accNumber)
            ? '1px solid #D0D5DD'
            : 0,
          borderRadius: '4px',
          width: expandedTariffs.includes(account.accNumber) ? '50%' : '100%',
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignContent={'center'}
          alignItems={'center'}
        >
          <Stack
            direction="row"
            alignContent={'center'}
            alignItems={'center'}
            gap={'5px'}
          >
            <Checkbox
              icon={<CheckBoxIcon />}
              checked={
                !!selectedAccounts.find(
                  (acc) => acc.accNumber === account.accNumber
                )
              }
              onChange={() => handleLinkedStatus(account.accNumber)}
            />
            <Typography variant="label2">
              Account {account.accNumber} - {account.accClassDesc}
            </Typography>
            <Typography
              sx={{
                border: `1px solid ${account.customerType === 'I' ? '#B9E6FE' : '#ABEFC6'}`,
                borderRadius: '4px',
                px: '5px',
                py: '0px',
                mx: '5px',
                color: account.customerType === 'I' ? '#026AA2' : '#067647',
                background:
                  account.customerType === 'I' ? '#F0F9FF' : '#ECFDF3',
              }}
              variant="label2"
            >
              {accountType(account.customerType)}
            </Typography>
          </Stack>
          <Stack
            direction="row"
            gap={'5px'}
            sx={{
              marginRight: '3%',
              padding: '0 2px',
              // display: 'none',

              cursor: 'pointer',
              border: '1px solid #D0D5DD',
              borderRadius: '6px',
            }}
            onClick={() => {
              handleExpandCard(account.accNumber)
              handleLinkedStatus(account.accNumber)
            }}
          >
            <Typography variant="label1" color={'primary.primary3'}>
              {expandedCard.includes(account.accNumber) ? 'Collapse' : 'Expand'}
            </Typography>
            {expandedCard.includes(account.accNumber) ? (
              <Expandless />
            ) : (
              <Expandmore />
            )}
          </Stack>
        </Stack>
        {expandedCard.includes(account.accNumber) && (
          <Stack
            sx={{
              px: '7%',
              py: '2%',
              gap: '1vh',
              flexDirection: 'column',
            }}
          >
            <Stack
              direction="row"
              justifyContent="space-between"
              alignContent={'center'}
              alignItems={'center'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                p: '2% 1%',
              }}
            >
              <Stack
                flexDirection={'row'}
                gap={'0.6rem'}
                sx={{
                  px: '1%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <TariffIcon height="20" width="20" stroke="#2A3339" />
                <Stack
                  sx={{ display: 'flex', flexDirection: 'row', gap: '1rem' }}
                >
                  <Typography variant="body2" color={'text.primary'}>
                    Tariffs
                  </Typography>
                  {isTariffSelectedAndSaved && (
                    <TariffCheckedIcon
                      height="20"
                      width="20"
                      stroke="#555C61"
                    />
                  )}
                </Stack>
              </Stack>

              <Stack
                // onClick={() => handleExpandTariffs(account.accNumber)}
                onClick={() => handleExpand(account.accNumber, 'tariffs')}
                sx={{ cursor: 'pointer' }}
              >
                {expandedTariffs.includes(account.accNumber) ? (
                  <LeftIcon height="20" width="20" stroke="#2A3339" />
                ) : (
                  <RightIcon height="20" width="20" stroke="#2A3339" />
                )}
              </Stack>
            </Stack>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignContent={'center'}
              alignItems={'center'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                p: '2% 1%',
              }}
            >
              <Stack
                flexDirection={'row'}
                gap={'0.6rem'}
                sx={{
                  px: '1%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <NotificationsIcon height="20" width="20" stroke="#2A3339" />
                <Typography variant="body2" color={'text.primary'}>
                  Notifications
                </Typography>
              </Stack>

              <Stack
                // onClick={() => handleExpandNotifications(account.accNumber)}
                onClick={() => handleExpand(account.accNumber, 'notifications')}
                sx={{ cursor: 'pointer' }}
              >
                {expandedNotifications.includes(account.accNumber) ? (
                  <LeftIcon height="20" width="20" stroke="#2A3339" />
                ) : (
                  <RightIcon height="20" width="20" stroke="#2A3339" />
                )}
              </Stack>
            </Stack>
            {/* {expandedNotifications.includes(account.accNumber) && (
              <NotificationsView />
            )} */}

            <Stack
              direction="row"
              justifyContent="space-between"
              alignContent={'center'}
              alignItems={'center'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                p: '2% 1%',
              }}
            >
              <Stack
                flexDirection={'row'}
                gap={'0.6rem'}
                sx={{
                  px: '1%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <EstatementIcon height="20" width="20" stroke="#2A3339" />
                <Typography variant="body2" color={'text.primary'}>
                  E-Statements
                </Typography>
              </Stack>
              <Stack
                onClick={() => handleExpand(account.accNumber, 'statements')}
                sx={{ cursor: 'pointer' }}
              >
                {expandedStatements.includes(account.accNumber) ? (
                  <LeftIcon height="20" width="20" stroke="#2A3339" />
                ) : (
                  <RightIcon height="20" width="20" stroke="#2A3339" />
                )}
              </Stack>
            </Stack>
            {/* {expandedStatements.includes(account.accNumber) && (
              <StatementsView />
            )} */}

            <Stack
              direction="row"
              justifyContent="space-between"
              alignContent={'center'}
              alignItems={'center'}
              sx={{
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                p: '2% 1%',
              }}
            >
              <Stack
                flexDirection={'row'}
                gap={'0.6rem'}
                sx={{
                  px: '1%',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  alignContent: 'center',
                }}
              >
                <BalanceAlertIcon height="20" width="20" stroke="#2A3339" />
                <Typography variant="body2" color={'text.primary'}>
                  Periodic Balance Alerts
                </Typography>
              </Stack>

              <Stack
                onClick={() => handleExpand(account.accNumber, 'balanceAlerts')}
                sx={{ cursor: 'pointer' }}
              >
                {expandedBalanceAlerts.includes(account.accNumber) ? (
                  <LeftIcon height="20" width="20" stroke="#2A3339" />
                ) : (
                  <RightIcon height="20" width="20" stroke="#2A3339" />
                )}
              </Stack>
            </Stack>
          </Stack>
        )}
      </Stack>
      {expandedTariffs.includes(account.accNumber) && (
        <TariffsView
          handleExpand={() => handleExpand(account.accNumber, 'tariffs')}
          account={account}
          onUpdateAccount={onUpdateAccount}
          setIsTariffSelectedAndSaved={setIsTariffSelectedAndSaved}
        />
      )}
      {expandedNotifications.includes(account.accNumber) && (
        <NotificationsView />
      )}
      {expandedStatements.includes(account.accNumber) && <StatementsView />}
      {expandedBalanceAlerts.includes(account.accNumber) && (
        <PeriodicAlertsView />
      )}
    </Stack>
  )
}

export const TariffsView = ({
  handleExpand,
  account,
  onUpdateAccount,
  setIsTariffSelectedAndSaved,
}: {
  handleExpand: () => void
  account: ICustomerAccount
  onUpdateAccount: (updatedAcc: ICustomerAccount) => void
  setIsTariffSelectedAndSaved: (value: boolean) => void
}) => {
  const [selectedTariff, setSelectedTariff] = useState<string | null>(null)
  const { tariffs, tariffsLoading } = useAppSelector((state) => state.customers)
  const dispatch = useAppDispatch()

  useEffect(() => {
    getAccountTariffs(dispatch)
  }, [])

  const onSelect = (event: { target: { value: string } }) => {
    const selectedValue = event.target.value
    setSelectedTariff((prev) => (prev === selectedValue ? null : selectedValue))
  }

  const handleSave = () => {
    if (selectedTariff) {
      const updatedAcc = { ...account, tariffName: selectedTariff }
      onUpdateAccount(updatedAcc)
      setIsTariffSelectedAndSaved(true)
      handleExpand()
      console.log('Updated Account', updatedAcc)
    }
  }

  return (
    <Stack
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '4px',
        px: '2%',
        width: '60%',
      }}
    >
      <Stack
        flexDirection={'row'}
        sx={{
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <Stack>
          <Typography fontWeight={'bold'} sx={{ mt: '10px' }}>
            Tariff
          </Typography>
          <Typography>
            Please select the tariff you want to assign to this account.
          </Typography>
        </Stack>
        <Stack
          flexDirection={'row'}
          gap={2}
          sx={{ justifyContent: 'space-between' }}
        >
          <Button
            variant="contained"
            sx={{ cursor: 'pointer' }}
            startIcon={<CheckIcon />}
            onClick={handleSave}
          >
            {' '}
            Save{' '}
          </Button>
          <CustomIconButton
            icon={IconClose}
            border="1.5px solid #D0D5DD"
            onClick={() => handleExpand()}
          />
        </Stack>
      </Stack>
      <Divider />
      <Stack
        direction="column"
        sx={{
          borderRadius: '4px',
          px: '2%',
        }}
      >
        {tariffsLoading ? (
          <CustomSkeleton
            variant="rectangular"
            sx={{
              height: '1rem',
              width: '37rem',
            }}
          />
        ) : (
          tariffs &&
          tariffs.map((tariff) => (
            <Typography
              key={tariff.name}
              variant="body2"
              sx={{
                display: 'flex',
                alignItems: 'center',
              }}
              color="text.primary"
            >
              <Radio
                checked={selectedTariff === tariff.name}
                onChange={onSelect}
                value={tariff.name}
                name="radio-buttons"
                inputProps={{ 'aria-label': tariff.name }}
                sx={{
                  ml: {
                    xs: '0.5rem',
                    sm: '-1.0625rem',
                  },
                }}
              />
              {tariff.name}
            </Typography>
          ))
        )}
      </Stack>
    </Stack>
  )
}

export const NotificationsView = () => {
  return (
    <Stack
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '4px',
        px: '2%',
        width: '100%',
      }}
    >
      <Stack>
        <Typography
          variant="body2"
          sx={{
            display: 'flex',
            alignItems: 'center',
          }}
          color="text.primary"
        >
          <Checkbox icon={<CheckBoxIcon />} />
          Credit & Debit
        </Typography>
        <Stack
          sx={{
            paddingLeft: '2%',
          }}
        >
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            Notify of transactions above KES
            <TextField
              variant="standard"
              size="small"
              sx={{
                marginLeft: '5px',
              }}
            />
          </Typography>
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
            }}
          >
            Channels
          </Typography>
          <Stack direction="row" gap={'3%'}>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              SMS
            </Typography>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              Email
            </Typography>
          </Stack>
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
            }}
          >
            Phone Numbers
          </Typography>
          <Stack direction="row" gap={'3%'}>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              0712345678
            </Typography>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              **********
            </Typography>
          </Stack>
        </Stack>
      </Stack>
      <Divider />
      <Stack>
        <Typography
          variant="body2"
          sx={{
            display: 'flex',
            alignItems: 'center',
          }}
          color="text.primary"
        >
          <Checkbox icon={<CheckBoxIcon />} />
          Overdrawn account
        </Typography>
        <Stack
          sx={{
            paddingLeft: '2%',
          }}
        >
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            Notify of transactions above KES
            <TextField
              variant="standard"
              size="small"
              sx={{
                marginLeft: '5px',
              }}
            />
          </Typography>
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
            }}
          >
            Channels
          </Typography>
          <Stack direction="row" gap={'3%'}>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              SMS
            </Typography>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              Email
            </Typography>
          </Stack>
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              paddingLeft: '2%',
            }}
          >
            Phone Numbers
          </Typography>
          <Stack direction="row" gap={'3%'}>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              0712345678
            </Typography>
            <Typography variant="body2" color="text.primary">
              <Checkbox icon={<CheckBoxIcon />} />
              **********
            </Typography>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}

export const StatementsView = () => {
  return (
    <Stack
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '4px',
        px: '2%',
        width: '100%',
      }}
    >
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Daily
      </Typography>
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Weekly
      </Typography>
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Monthly
      </Typography>
    </Stack>
  )
}
export const PeriodicAlertsView = () => {
  return (
    <Stack
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '4px',
        px: '2%',
        width: '100%',
      }}
    >
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Daily Alerts
      </Typography>
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Weekly Alerts
      </Typography>
      <Typography variant="body2" color="text.primary">
        <Checkbox icon={<CheckBoxIcon />} />
        Monthly Alerts
      </Typography>
    </Stack>
  )
}
