'use client'

import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import ArrowF<PERSON>wardIcon from '@mui/icons-material/ArrowForward'
import { Button, Stack, Typography } from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'

import { IAccountsLinkProps } from '@/app/dashboard/customers/customer/Details/Accounts/Create/AccountsSummary'
import { ICustomerAccount } from '@/app/interfaces/customers'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'
import { getUnlinkedCustomerAccountsByProfileId } from '@/app/redux/actions/customers'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { AccountsCard } from '@/app/dashboard/customers/customer/Details/Accounts/Create/AccountsCard'

export type ExpandableView =
  | 'tariffs'
  | 'notifications'
  | 'statements'
  | 'balanceAlerts'

export const AccountsStep = ({ formik, setStep }: IAccountsLinkProps) => {
  const { customer, customerUnlinkedAccountsList, isLoadingUnlinkedAccounts } =
    useAppSelector((state) => state.customers)
  const dispatch = useAppDispatch()
  const [expanded, setExpanded] = useState<string[]>([])
  const [selectedAccounts, setSelectedAccounts] = useState<ICustomerAccount[]>(
    formik.values.accounts
  )

  const [expandedNotifications, setExpandedNotifications] = useState<string[]>(
    []
  )
  const [expandedStatements, setExpandedStatements] = useState<string[]>([])
  const [expandedBalanceAlerts, setExpandedBalanceAlerts] = useState<string[]>(
    []
  )

  //===============starts here -==================*******************
  const [expandedTariffs, setExpandedTariffs] = useState<string[]>([])
  const [expandedCard, setExpandedCard] = useState<string[]>([])
  const onUpdateAccount = (updatedAcc: ICustomerAccount) => {
    setSelectedAccounts((prevAccounts) =>
      prevAccounts.map((acc) =>
        acc.accNumber === updatedAcc.accNumber ? updatedAcc : acc
      )
    )
  }

  const handleExpandCard = (accNumber: string) => {
    if (expandedCard.includes(accNumber)) {
      setExpandedCard(expandedCard.filter((acc) => acc !== accNumber))
      setExpanded([])
      setExpandedTariffs([])
      setExpandedNotifications([])
      setExpandedStatements([])
      setExpandedBalanceAlerts([])
    } else {
      setExpandedCard([...expandedCard, accNumber])
    }
  }
  //===============Ends here -==================*******************

  // const handleExpand = (accNumber: string) => {
  //   if (expanded.includes(accNumber)) {
  //     setExpanded(expanded.filter((acc) => acc !== accNumber))
  //   } else {
  //     setExpanded([...expanded, accNumber])
  //   }
  // }

  const handleExpand = useCallback(
    (accNumber: string, type: ExpandableView) => {
      // Reset all expanded states to an empty array except the target view. Target view is the one we are looking to expand
      setExpanded([])
      setExpandedTariffs([])
      setExpandedNotifications([])
      setExpandedStatements([])
      setExpandedBalanceAlerts([])

      switch (type) {
        case 'tariffs':
          const isExpandedTariffs = expandedTariffs.includes(accNumber)
          setExpandedTariffs(isExpandedTariffs ? [] : [accNumber])
          console.log('Updated expandedTariffs:', expandedTariffs)
          break
        case 'notifications':
          const isExpandedNotifications =
            expandedNotifications.includes(accNumber)
          setExpandedNotifications(isExpandedNotifications ? [] : [accNumber])
          console.log('Updated expandedNotifications:', expandedNotifications)
          break
        case 'statements':
          const isExpandedStatements = expandedStatements.includes(accNumber)
          setExpandedStatements(isExpandedStatements ? [] : [accNumber])
          break
        case 'balanceAlerts':
          const isExpandedBalanceAlerts =
            expandedBalanceAlerts.includes(accNumber)
          setExpandedBalanceAlerts(isExpandedBalanceAlerts ? [] : [accNumber])
          break
      }
    },
    [
      expandedTariffs,
      expandedNotifications,
      expandedStatements,
      expandedBalanceAlerts,
    ]
  )

  const changeLinkedAccountStatus = (accNumber: string) => {
    if (selectedAccounts.find((acc) => acc.accNumber === accNumber)) {
      setSelectedAccounts(
        selectedAccounts.filter((acc) => acc.accNumber !== accNumber)
      )
    } else {
      const account = customerUnlinkedAccountsList.find(
        (acc) => acc.accNumber === accNumber
      )
      if (account) {
        setSelectedAccounts([...selectedAccounts, account])
      }
    }
  }
  const activeAccounts = () => {
    const accounts: ICustomerAccount[] = []
    customerUnlinkedAccountsList.forEach((account) => {
      if (account.accDormant === 'N') {
        accounts.push(account)
      }
    })
    return accounts
  }
  const dormantAccounts = () => {
    const accounts: ICustomerAccount[] = []
    customerUnlinkedAccountsList.forEach((account) => {
      if (account.accDormant !== 'N') {
        accounts.push(account)
      }
    })
    return accounts
  }
  useEffect(() => {
    getUnlinkedCustomerAccountsByProfileId(
      customer.id ? customer.id : '',
      dispatch
    )
  }, [])
  const handleNext = () => {
    formik.setFieldValue('accounts', selectedAccounts)
    setStep('Summary')
  }

  return (
    <Stack
      sx={{
        justifyContent: 'center',
        alignItems: 'center',
      }}
      direction="column"
    >
      <Stack
        sx={{
          alignContent: 'center',
          alignItems: 'center',
          py: '2%',
          gap: '1vh',
          width: '60%',
        }}
      >
        <Typography variant="body1" color="primary.main">
          STEP 1 OF 2
        </Typography>
        <Typography variant="h6">Link Accounts</Typography>
        <Typography
          variant="label1"
          textAlign="center"
          color="primary.primary3"
        >
          Click on the checkbox of the accounts you want to link, then on the
          dropdown icon to setup preferences for each account.
        </Typography>
      </Stack>
      {isLoadingUnlinkedAccounts ? (
        <CustomSkeleton
          animation="wave"
          variant="rectangular"
          width="70%"
          height="60vh"
        />
      ) : (
        <Stack
          sx={{
            width: '70%',
          }}
          gap={'2vh'}
        >
          <Typography color="primary.primary3">Active</Typography>
          {(activeAccounts() as ICustomerAccount[]).map((account) => (
            <AccountsCard
              key={account.accNumber}
              handleLinkedStatus={changeLinkedAccountStatus}
              account={account}
              selectedAccounts={selectedAccounts}
              onUpdateAccount={onUpdateAccount}
              handleExpand={handleExpand}
              handleExpandCard={handleExpandCard}
              expanded={expanded}
              expandedCard={expandedCard}
              expandedTariffs={expandedTariffs}
              expandedNotifications={expandedNotifications}
              expandedStatements={expandedStatements}
              expandedBalanceAlerts={expandedBalanceAlerts}
            />
          ))}
          {dormantAccounts().length > 0 && (
            <Stack>
              <Typography color="primary.primary3">Dormant</Typography>
              {(dormantAccounts() as ICustomerAccount[]).map((account) => (
                <AccountsCard
                  key={account.accNumber}
                  handleLinkedStatus={changeLinkedAccountStatus}
                  account={account}
                  selectedAccounts={selectedAccounts}
                  onUpdateAccount={onUpdateAccount}
                  handleExpand={handleExpand}
                  handleExpandCard={handleExpandCard}
                  expanded={expanded}
                  expandedCard={expandedCard}
                  expandedTariffs={expandedTariffs}
                  expandedNotifications={expandedNotifications}
                  expandedStatements={expandedStatements}
                  expandedBalanceAlerts={expandedBalanceAlerts}
                />
              ))}
            </Stack>
          )}
        </Stack>
      )}
      <Stack
        sx={{ width: '70%', py: '3vh' }}
        direction="row"
        justifyContent="space-between"
        gap="4%"
      >
        <Button
          variant="outlined"
          fullWidth
          startIcon={<ArrowBackIcon />}
          onClick={() => setStep('Personal Details')}
        >
          Back
        </Button>
        <Button
          variant="contained"
          fullWidth
          disabled={selectedAccounts.length === 0}
          endIcon={<ArrowForwardIcon />}
          onClick={handleNext}
        >
          Next
        </Button>
      </Stack>
    </Stack>
  )
}
