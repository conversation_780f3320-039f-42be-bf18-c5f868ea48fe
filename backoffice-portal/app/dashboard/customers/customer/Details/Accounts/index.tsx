import { useEffect } from 'react'
import { Stack, Typography } from '@mui/material'

import { AccountsEmptyState } from '@/app/dashboard/customers/customer/Details/Accounts/EmptyState'
import { LoadingListsSkeleton } from '@/app/components/Loading'
import { getLinkedCustomerAccountsByProfileId } from '@/app/redux/actions/customers'
import { SingleAccountView } from '@/app/dashboard/customers/customer/Details/Accounts/SingleAccountView'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { AccountsList } from '@/app/dashboard/customers/customer/Details/Accounts/AccountsList'
import { AccountsHeader } from '@/app/dashboard/customers/customer/Details/Accounts/Header'

const CustomerAccounts = () => {
  const {
    isViewAccountOpen,
    customer,
    isLoadingAccounts,
    customerLinkedAccountsList,
  } = useAppSelector((state) => state.customers)
  const dispatch = useAppDispatch()
  useEffect(() => {
    customer &&
      getLinkedCustomerAccountsByProfileId(
        customer.id ? customer.id : '',
        dispatch
      )
  }, [customer])
  return (
    <Stack
      direction="column"
      gap={'1vh'}
      sx={{
        px: '1.5%',
      }}
    >
      {!isViewAccountOpen ? (
        <>
          <Typography variant="h6"> Accounts</Typography>
          <AccountsHeader customer={customer} />
        </>
      ) : null}
      {isLoadingAccounts ? (
        <LoadingListsSkeleton />
      ) : isViewAccountOpen ? (
        <SingleAccountView />
      ) : (
        <>
          {customerLinkedAccountsList.length < 1 ? (
            <AccountsEmptyState />
          ) : (
            <AccountsList />
          )}
        </>
      )}
    </Stack>
  )
}
export default CustomerAccounts
