import { EditOutlined, History, MoreHoriz } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import {
  Button,
  Chip,
  Divider,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect } from 'react'

import { AddNotificationDialog } from '@/app/dashboard/customers/customer/Details/Accounts/AccountViewDialogs'
import {
  ActivateAccount,
  DeactivateAccount,
  RestrictAccount,
  UnlinkAccount,
  ViewAccountHistory,
} from '@/app/dashboard/customers/customer/Details/Accounts/AccountsMoreMenu'
import { CustomerStatusChip } from '@/app/components/Chip'
import { ICustomerAccount } from '@/app/interfaces/customers'
import {
  setIsViewAccountOpen,
  setSelectedAccount,
} from '@/app/redux/reducers/customers'
import {
  AccountDetailsIcon,
  AccountIcon,
  NotificationDetailsIcon,
  StatementDetailsIcon,
} from '@/app/components/SvgIcons/CustomerIcons'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  getCustomerAccountsLogsBackoffice,
  getCustomersAccountHistory,
} from '@/app/redux/actions/customers'

import AccountHistory from './History'

const notifications = [
  {
    type: 'Credit & Debit',
    status: 'Active',
    dateCreated: '12/12/2021',
    channels: ['Email', 'SMS'],
    phoneNumbers: ['**********', '**********'],
  },
  {
    type: 'Daily Balance',
    status: 'Active',
    dateCreated: '12/12/2021',
    channels: ['Email', 'SMS'],
    phoneNumbers: ['**********', '**********'],
  },
  {
    type: 'Overdraft',
    status: 'Active',
    dateCreated: '12/12/2021',
    channels: ['Email', 'SMS'],
    phoneNumbers: ['**********', '**********'],
  },
]
const statements = [
  {
    type: 'Daily Subscription',
    status: 'Active',
    dateCreated: '12/12/2021',
    email: '<EMAIL>',
    charges: 'KES 100',
  },
  {
    type: 'Monthly Subscription',
    status: 'Active',
    dateCreated: '12/12/2021',
    email: '<EMAIL>',
    charges: 'KES 10',
  },
]
export const SingleAccountView = () => {
  const { customerProfileAccount, customer } = useAppSelector(
    (state) => state.customers
  )

  const buttons = [
    {
      Button: <ViewAccountHistory />,
      key: 'history',
    },
    {
      Button: <RestrictAccount />,
      key: 'restrict',
    },
    {
      Button:
        customerProfileAccount.status === 'INACTIVE' ? (
          <ActivateAccount
            isMainMenu={false}
            account={customerProfileAccount}
            customer={customer}
          />
        ) : (
          <DeactivateAccount
            isMainMenu={false}
            account={customerProfileAccount}
            customer={customer}
          />
        ),
      key: customerProfileAccount.isBlocked ? 'activate' : 'deactivate',
    },
    {
      Button: (
        <UnlinkAccount account={customerProfileAccount} customer={customer} />
      ),
      key: 'unlink',
    },
  ]
  const dispatch = useAppDispatch()
  const handleClose = () => {
    dispatch(setIsViewAccountOpen(false))
    dispatch(setSelectedAccount({} as ICustomerAccount))
  }

  useEffect(() => {
    getCustomerAccountsLogsBackoffice(
      dispatch,
      customerProfileAccount?.accountNo
    )
    getCustomersAccountHistory({
      dispatch,
      profileID: customerProfileAccount?.profile.id,
      accountNo: customerProfileAccount?.accountNo,
    })
  }, [])
  return (
    <Stack direction="row" gap={'2vw'}>
      <Stack
        sx={{
          width: '22%',
          gap: '2vh',
        }}
        direction="column"
      >
        <IconButton
          sx={{
            border: '1px solid #AAADB0',
            borderRadius: '8px',
            fontWeight: '500',
            width: '20%',
          }}
          onClick={handleClose}
        >
          <ArrowBackIcon />
        </IconButton>
        <AccountIcon />
        <Typography
          variant="subtitle1"
          sx={{
            textWrap: 'noWrap',
          }}
        >
          Account {customerProfileAccount.accountNo}
        </Typography>
        <CustomerStatusChip
          label={customerProfileAccount.status}
          sx={{
            width: 'fit-content',
          }}
        />
        <Stack>
          {buttons.map((button) => (
            <Stack key={button.key}>
              {button.Button}
              <Divider />
            </Stack>
          ))}
        </Stack>
      </Stack>
      <Stack
        sx={{
          width: '78%',
          gap: '3vh',
        }}
        direction="column"
      >
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <AccountDetailsIcon />
              Account Details
            </Typography>
            <Stack direction="row" gap={'5px'}>
              <AccountHistory
                onHistoryButton={async () => {
                  await getCustomerAccountsLogsBackoffice(
                    dispatch,
                    customerProfileAccount?.accountNo
                  )
                  await getCustomersAccountHistory({
                    dispatch,
                    profileID: customerProfileAccount?.profile.id,
                    accountNo: customerProfileAccount?.accountNo,
                  })
                }}
              />
              <Button
                variant="outlined"
                startIcon={<EditOutlined />}
                size="small"
                sx={{
                  height: '35px',
                  border: '1px solid #E3E4E4',
                }}
                disabled={customer.isBlocked}
              >
                Edit
              </Button>
            </Stack>
          </Stack>

          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '2vw',
            }}
          >
            <TextField
              fullWidth
              label={'Branch'}
              margin={'normal'}
              value={customerProfileAccount.branchCode}
              size="small"
            />
            <TextField
              fullWidth
              label={'Account Name'}
              margin={'normal'}
              value={customerProfileAccount.fullName}
              size="small"
            />
            <TextField
              fullWidth
              label={'Account Alias'}
              margin={'normal'}
              value={customerProfileAccount.shortName}
              size="small"
            />
          </Stack>
          <Stack direction="row" gap={'2vw'}>
            <TextField
              fullWidth
              label={'Account Number'}
              margin={'normal'}
              value={customerProfileAccount.accountNo}
              size="small"
            />
            <TextField
              fullWidth
              label={'Status'}
              margin={'normal'}
              value={customerProfileAccount.status}
              size="small"
            />
            <TextField
              fullWidth
              label={'Currency'}
              margin={'normal'}
              value={customerProfileAccount.currency}
              size="small"
            />
          </Stack>
          <Stack direction="row" gap={'2vw'}>
            <TextField
              fullWidth
              label={'Tariff'}
              margin={'normal'}
              value={customerProfileAccount.tariffName}
              size="small"
            />
            <TextField
              fullWidth
              label={'Type'}
              margin={'normal'}
              value={customerProfileAccount.accountType}
              size="small"
            />
            <TextField
              fullWidth
              label={'Phone Number'}
              margin={'normal'}
              value={
                customerProfileAccount.profile.phoneNumber
                  ? customerProfileAccount.profile.phoneNumber
                  : 'Not provided'
              }
              size="small"
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
            gap: '2vh',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <NotificationDetailsIcon />
              Notifications/Alerts
            </Typography>
            <Stack direction="row" gap={'5px'}>
              <Button
                variant="outlined"
                sx={{
                  height: '35px',
                  px: '10%',
                  border: '1px solid #E3E4E4',
                }}
                size="small"
                startIcon={<History />}
              >
                History
              </Button>
              <AddNotificationDialog />
            </Stack>
          </Stack>
          <NotificationsList />
        </Stack>
        <Stack
          sx={{
            border: '1px solid #E3E4E4',
            borderRadius: '8px',
            padding: '2%',
            gap: '2vh',
          }}
        >
          <Stack direction="row" justifyContent="space-between">
            <Typography
              variant="subtitle2"
              color="text.primary"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '5px',
              }}
            >
              <StatementDetailsIcon />
              E-statement subscriptions
            </Typography>
            <Stack direction="row" gap={'5px'}>
              <Button
                variant="outlined"
                sx={{
                  height: '35px',
                  px: '10%',
                  border: '1px solid #E3E4E4',
                }}
                size="small"
                startIcon={<History />}
              >
                History
              </Button>
              <Button
                variant="outlined"
                startIcon={<AddOutlinedIcon />}
                size="small"
                sx={{
                  height: '35px',
                  border: '1px solid #E3E4E4',
                  textWrap: 'noWrap',
                  background: '#F8F9FC',
                }}
                disabled={customer.isBlocked}
              >
                Add Subscription
              </Button>
            </Stack>
          </Stack>
          <StatementsList />
        </Stack>
      </Stack>
    </Stack>
  )
}

const NotificationsList = () => {
  return (
    <TableContainer
      component={Paper}
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Table sx={{}} aria-label="simple table" size="small">
        <TableHead
          sx={{
            background: '#F9FAFB',
          }}
        >
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Date Opted In</TableCell>
            <TableCell>Channels</TableCell>
            <TableCell>Phone Numbers</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {notifications.map((notification) => (
            <TableRow key={notification.type}>
              <TableCell>{notification.type}</TableCell>
              <TableCell>
                <CustomerStatusChip label={notification.status.toUpperCase()} />
              </TableCell>
              <TableCell>{notification.dateCreated}</TableCell>
              <TableCell>
                {notification.channels.map((chan) => (
                  <Chip
                    key={chan}
                    label={chan}
                    sx={{
                      marginRight: '5px',
                    }}
                  />
                ))}
              </TableCell>
              <TableCell>
                {notification.phoneNumbers.map((phone) => (
                  <Chip
                    key={phone}
                    label={phone}
                    sx={{
                      marginRight: '5px',
                    }}
                  />
                ))}
              </TableCell>
              <TableCell>
                <MoreHoriz />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  )
}
const StatementsList = () => {
  return (
    <TableContainer
      component={Paper}
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Table sx={{}} aria-label="simple table" size="small">
        <TableHead
          sx={{
            background: '#F9FAFB',
          }}
        >
          <TableRow>
            <TableCell>Type</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Date Opted In</TableCell>
            <TableCell>Email</TableCell>
            <TableCell>Charge Per Statement</TableCell>
            <TableCell></TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {statements.map((statement) => (
            <TableRow key={statement.type}>
              <TableCell>{statement.type}</TableCell>
              <TableCell>
                <CustomerStatusChip label={statement.status.toUpperCase()} />
              </TableCell>
              <TableCell>{statement.dateCreated}</TableCell>
              <TableCell>{statement.email}</TableCell>
              <TableCell>{statement.charges}</TableCell>
              <TableCell>
                <MoreHoriz />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  )
}
