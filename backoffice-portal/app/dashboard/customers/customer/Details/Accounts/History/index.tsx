import React from 'react'
import { CloseRounded } from '@mui/icons-material'
import {
  Box,
  But<PERSON>,
  Drawer,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material'

import CustomerAccountsHistory from './CustomerAccountsHistory'

interface IAccountHistory {
  onHistoryButton?: () => void
}

const AccountHistory: React.FC<IAccountHistory> = ({ onHistoryButton }) => {
  const [open, setOpen] = React.useState(false)
  return (
    <>
      <Button
        variant={'outlined'}
        onClick={() => {
          setOpen((prev) => !prev)
          onHistoryButton && onHistoryButton()
        }}
        sx={{
          width: '100%',
          height: '36px',
          border: '1px solid #D0D5DD',
        }}
      >
        History
      </Button>
      <Drawer open={open} anchor="right">
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">Account History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack>
            <CustomerAccountsHistory />
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}

export default AccountHistory
