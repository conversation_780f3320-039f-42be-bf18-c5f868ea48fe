import {
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import React from 'react'
import dayjs from 'dayjs'
import { Tabs } from '@mui/base/Tabs'
import { SearchRounded } from '@mui/icons-material'

import { DotsDropdown } from '@/app/components/DropDownMenus/Index'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { Tab, TabPanel, TabsList } from '@/app/components/CustomToggle'
import { useAppSelector } from '@/app/redux'
import { CustomSearchInput } from '@/app/components/Input/CustomSearchInput'
import { ITableData } from '@/app/interfaces/shared'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
const header = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker_timestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker_timestamp',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: '',
    alignCenter: false,
    alignRight: false,
  },
]
const customerHead = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'eventDate',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
]

const CustomerAccountsHistory = () => {
  const [tab, setTab] = React.useState<number>(0)
  const [search, setSearch] = React.useState<string>('')
  const {
    accountLogs,
    isLoadingAccountsLogs,
    accountLogsBackOffice,
    isLoadingAccountsLogsBackOffice,
  } = useAppSelector((state) => state.customers)

  type TableData = IApprovalRequest | ITableData

  const isApprovalRequest = (data: TableData): data is IApprovalRequest => {
    return (data as IApprovalRequest).maker !== undefined
  }

  const isCustomerEvent = (data: TableData): data is ITableData => {
    return (data as ITableData).eventDate !== undefined
  }
  const searchLogs = (
    logs: IApprovalRequest[] | ITableData[],
    search: string
  ) => {
    const strings: string[] = []

    const traverse = (current: IApprovalRequest | ITableData) => {
      for (const value of Object.values(current)) {
        if (typeof value === 'string') {
          strings.push(value)
        } else if (typeof value === 'object' && value !== null) {
          traverse(value)
        }
      }
    }
    return logs?.filter((log) => {
      traverse(log)
      return strings.some((value) =>
        value
          .toString()
          .toLowerCase()
          .replaceAll(' ', '')
          .includes(search.toLowerCase().replaceAll(' ', ''))
      )
    })
  }

  const filteredBackOfficeLogs = React.useMemo(() => {
    return tab === 0 ? searchLogs(accountLogsBackOffice, search) : []
  }, [accountLogsBackOffice, search])

  const filteredCustomerLogs = React.useMemo(() => {
    return tab === 1 ? searchLogs(accountLogs, search) : []
  }, [accountLogs, search])

  const renderTable = (filteredData: ITableData[] | IApprovalRequest[]) => {
    return (
      <TableContainer
        sx={{
          maxHeight: '83vh',
          overflowX: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'lightgray transparent',
            padding: '0px 4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'darkgray',
            borderRadius: '10px',
          },
        }}
      >
        <Table stickyHeader>
          <CustomTableHeader
            order={'desc'}
            orderBy={''}
            rowCount={0}
            headLabel={tab === 0 ? [...header] : [...customerHead]}
            numSelected={0}
          />
          <TableBody>
            {filteredData &&
              filteredData.map((row: IApprovalRequest | ITableData) => {
                isApprovalRequest(row)
                return tab === 0
                  ? !isLoadingAccountsLogsBackOffice &&
                      accountLogsBackOffice &&
                      isApprovalRequest(row) && (
                        <TableRow
                          key={row.id}
                          sx={{
                            background: 'white',
                          }}
                        >
                          <TableCell>{row.makerCheckerType.name}</TableCell>
                          <TableCell>{row.maker}</TableCell>
                          <TableCell>
                            {dayjs(row.dateCreated).format(
                              'MMMM D, YYYY HH:mm:ss'
                            )}
                          </TableCell>
                          <TableCell>{row.checker}</TableCell>
                          <TableCell>
                            {dayjs(row.dateModified).format(
                              'MMMM D, YYYY HH:mm:ss'
                            )}
                          </TableCell>

                          <TableCell>
                            <DotsDropdown
                              menuItems={[
                                {
                                  label: 'See more Details',
                                  onClick: () => {},
                                },
                              ]}
                            />
                          </TableCell>
                        </TableRow>
                      )
                  : !isLoadingAccountsLogs &&
                      accountLogs &&
                      isCustomerEvent(row) && (
                        <TableRow
                          key={row.id}
                          sx={{
                            background: 'white',
                          }}
                        >
                          <TableCell>{row.event}</TableCell>
                          <TableCell>
                            {dayjs(row.eventDate).format(
                              'MMMM D, YYYY HH:mm:ss'
                            )}
                          </TableCell>
                        </TableRow>
                      )
              })}
          </TableBody>
        </Table>
      </TableContainer>
    )
  }
  return (
    <Stack
      sx={{
        width: '100%',
        gap: '2vh',
        padding: '0 1% 0 1%',
      }}
    >
      <Tabs
        value={tab}
        onChange={(
          _event: React.SyntheticEvent<Element, Event> | null,
          newValue: number | string | null
        ) => {
          if (newValue !== null) {
            //onChange && onChange(Number(newValue))
            setTab(Number(newValue))
          }
        }}
      >
        <Stack
          direction="row"
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '1rem',
            width: '100%',
          }}
        >
          <CustomSearchInput
            sx={{
              width: '62%',
              '&.Mui-focused': {
                width: '62%',
              },
            }}
            startAdornment={<SearchRounded />}
            placeholder="Search Event"
            onChange={(e) => {
              setSearch(e.target.value)
            }}
            value={search}
          />
          <TabsList>
            <Tab value={0}>From Back Office</Tab>
            <Tab value={1}>From Customer</Tab>
          </TabsList>
        </Stack>
        <TabPanel value={0}>
          {renderTable(filteredBackOfficeLogs as IApprovalRequest[])}
        </TabPanel>
        <TabPanel value={1}>
          {renderTable(filteredCustomerLogs as ITableData[])}
        </TabPanel>
      </Tabs>
    </Stack>
  )
}

export default CustomerAccountsHistory
