import { CheckRounded, CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  ChipProps,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material'
import React from 'react'

import { IFilter, IHeadCell } from '@/app/interfaces/shared'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { setDocumentViewer } from '@/app/redux/reducers/customers'
import { CustomFilterBox } from '@/app/components/Input/CustomFilterBox'
import { TableDropDownMenu } from '@/app/components/DropDownMenus/Index'

import { dummyKyc } from './dummy'

const CustomTableCell = styled(TableCell)(() => ({
  padding: '8px 20px',
}))

interface KycChipProps extends ChipProps {
  isSuccess: boolean
}

const KycChip = styled(({ isSuccess, ...other }: KycChipProps) => {
  return (
    <Chip
      {...other}
      sx={{
        background: isSuccess ? '#E6F4EA' : '#FFF6ED',
        padding: '2px 6px 2px 8px',
        maxHeight: '22px',
        minWidth: '46px',
      }}
      label={
        <Box
          sx={{
            display: 'flex',
            gap: '4px',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Typography
            sx={{
              color: isSuccess ? '#027A48' : '#C4320A',
            }}
          >
            {other.label &&
              other.label?.toString().charAt(0).toUpperCase() +
                other.label?.toString().slice(1)}
          </Typography>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {isSuccess ? (
              <CheckRounded
                sx={{
                  width: '12px',
                  height: '12px',
                  color: '#12B76A',
                }}
              />
            ) : (
              <CloseRounded
                sx={{
                  width: '12px',
                  height: '12px',
                  color: '#EB0045',
                }}
              />
            )}
          </Box>
        </Box>
      }
    />
  )
})(() => ({
  padding: 0,
}))

const headers: IHeadCell[] = [
  {
    id: 'parameter',
    label: 'Parameter',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checked',
    label: 'Checked',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'score',
    label: 'Score',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'documents',
    label: 'Documents',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Actions',
    alignCenter: false,
    alignRight: false,
  },
]

const filters: IFilter[] = [
  {
    filterName: 'Checked',
    options: [
      { key: 'yes', value: 'yes', label: 'Yes' },
      { key: 'no', value: 'no', label: 'No' },
    ],
    type: 'dropdown/checkbox',
  },
  {
    filterName: 'Status',
    options: [
      { key: 'passed', value: 'passed', label: 'Passed' },
      { key: 'failed', value: 'failed', label: 'Failed' },
    ],
    type: 'dropdown/checkbox',
  },
  {
    filterName: 'KYC Initializition Date',
    options: [
      { key: '05212024', value: '05-21-2024', label: 'May 21, 2024' },
      { key: '04122024', value: '04-12-2024', label: 'April 12, 2024' },
    ],
    type: 'select',
  },
]
const KYC = () => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  /** component state */
  const [openFilter, setOpenFilter] = React.useState<boolean>(false)

  const [searchValue, setSearchValue] = React.useState<string>('')
  const [filteredData, setFilteredData] = React.useState(dummyKyc)
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    //dispatch(setUserSearchValue(e.target.value))
  }
  const handleFilter = (f: Record<string, string | string[]>) => {
    // Get keys
    const keys = Object.keys(f)

    const filterData = dummyKyc.filter((data) => {
      return keys.every((key) => {
        const searchValue = f[key]
        const values = Object.values(data)

        if (Array.isArray(searchValue)) {
          return searchValue.some((val) => values.includes(val))
        } else {
          return values.includes(searchValue)
        }
      })
    })
    setFilteredData(filterData)
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        px: '3%',
        py: '1%',
      }}
    >
      {/* header */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '15px',
          justifyContent: 'space-between',
        }}
      >
        {/* kyc date filter */}

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            flexDirection: 'column',
            gap: '11px',
          }}
        >
          {/* filter & search */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              width: '100%',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
            }}
          >
            <CustomFilterBox
              openFilter={openFilter}
              setOpenFilter={setOpenFilter}
              searchValue={searchValue}
              handleSearch={handleSearch}
              filters={filters}
              onFilterChange={handleFilter}
            />

            <Button
              variant="outlined"
              sx={{
                padding: '8px 42px',
                background: '#F9FAFB',
                color: '#1C1D21',
                border: '1px solid #D0D5DD',
                borderRadius: '4px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                height: '40px',
              }}
              disabled={customer.isBlocked}
            >
              Initiate KYC Check
            </Button>
          </Box>
        </Box>
      </Box>

      {/* KYC table */}
      <Paper
        elevation={0}
        sx={{
          borderRadius: '4px',
          border: '1px solid #EAECF0',
          background: ' #FEFEFE',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        }}
      >
        <TableContainer
          sx={{
            maxHeight: '65vh',
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'lightgray transparent',
              padding: '0px 4px',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'darkgray',
              borderRadius: '10px',
            },
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {headers.map((headCell, index) => (
                  <TableCell
                    key={index}
                    sx={{
                      height: '44px',
                      padding: '12px 24px',
                      background: '#F9FAFB',
                    }}
                  >
                    {headCell.label}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>

            <TableBody>
              {filteredData.map((row, index) => (
                <TableRow key={row.id || index}>
                  <CustomTableCell>{row.parameter}</CustomTableCell>
                  <CustomTableCell>
                    <KycChip
                      label={row.checked}
                      isSuccess={row.checked === 'yes' ? true : false}
                    />
                  </CustomTableCell>
                  <CustomTableCell>
                    <KycChip
                      label={row.status}
                      isSuccess={row.status === 'passed' ? true : false}
                    />
                  </CustomTableCell>
                  <CustomTableCell>{row.score}</CustomTableCell>
                  <CustomTableCell
                    sx={{
                      textDecoration: 'underline',
                      cursor: 'pointer',
                    }}
                  >
                    <Typography
                      // sx={{
                      //   textDecoration: 'underline',
                      //   cursor: 'pointer',
                      // }}
                      onClick={() =>
                        dispatch(
                          setDocumentViewer({
                            open: true,
                            imageUrl: row.documents,
                          })
                        )
                      }
                    >
                      {row.documents}
                    </Typography>
                  </CustomTableCell>
                  <CustomTableCell>
                    {/* <CellButton
                      sx={{
                        display: 'flex',
                        width: '107px',
                        padding: '9px 28px',
                        borderRadius: '6px',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: '0px',
                      }}
                      variant="outlined"
                      endIcon={<KeyboardArrowDownRounded />}
                    >
                      Actions
                    </CellButton> */}
                    <TableDropDownMenu
                      disabled={customer.isBlocked}
                      menuItems={[
                        {
                          label: 'View',
                          onClick: () => alert('View'),
                        },
                        {
                          label: 'Edit',
                          onClick: () => alert('Edit'),
                        },
                        {
                          label: 'Delete',
                          onClick: () => alert('Delete'),
                        },
                      ]}
                    />
                  </CustomTableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  )
}

export default KYC
