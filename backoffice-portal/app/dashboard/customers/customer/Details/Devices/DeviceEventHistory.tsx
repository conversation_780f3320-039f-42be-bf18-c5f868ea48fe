import CloseIcon from '@mui/icons-material/Close'
import {
  <PERSON><PERSON>,
  <PERSON>er,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import dayjs from 'dayjs'

import { CustomFilterBox } from '@/app/components/Input/CustomFilterBox'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { IFilter, IHeadCell } from '@/app/interfaces/shared'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { getCustomerDeviceHistory } from '@/app/redux/actions/customers'

const header: IHeadCell[] = [
  {
    id: 'event',
    label: 'Event',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'event_source',
    label: 'Event Source',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'event_date',
    label: 'Event Date',
    alignCenter: false,
    alignRight: false,
  },
]

const DeviceEventHistory = () => {
  const [openFilters, setOpenFilters] = React.useState(false)
  const dispatch = useAppDispatch()
  const {
    deviceLogs: events,
    IsLoadingDeviceLogs: loadingLogs,
    IsSuccessfulDeviceLogs: successLogs,
    customer,
    device,
  } = useAppSelector((state) => state.customers)

  const filters: IFilter[] = [
    {
      filterName: 'Device Type',
      options: [
        {
          key: 'App',
          value: 'App',
          label: 'App',
        },
        {
          key: 'USSD',
          value: 'USSD',
          label: 'USSD',
        },
      ],
      type: 'dropdown/single',
    },
  ]

  const handleSearch = () => {}
  const handleFilterChange = () => {}
  const [open, setOpen] = useState<boolean>(false)
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }

  React.useEffect(() => {
    const deviceID = device && device ? device.uuid : ''
    const customerID = customer.id ? customer?.id : ''
    getCustomerDeviceHistory(dispatch, customerID, deviceID)
  }, [device, customer.id])

  return (
    <>
      <Button
        onClick={() => setOpen(!open)}
        variant="outlined"
        sx={{
          height: '34px',
          px: '10%',
          border: '1px solid #D0D5DD;',
        }}
        disabled={customer.isBlocked}
      >
        <Typography
          sx={{
            textWrap: 'nowrap',
            textAlign: 'center',
          }}
          variant="label1"
        >
          Device History
        </Typography>
      </Button>
      <Drawer open={open} anchor={'right'} onClose={handleClose}>
        <Stack
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
            paddingLeft: '5%',
            paddingTop: '2%',
          }}
        >
          <Typography
            sx={{
              fontSize: '16px',
              fontWeight: 600,
              py: '1%',
            }}
          >
            Device History
          </Typography>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 4,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <Stack
          sx={{
            px: '5%',
            py: '3%',
            gap: '2vh',
            width: '100%',
          }}
        >
          <CustomFilterBox
            openFilter={openFilters}
            setOpenFilter={setOpenFilters}
            searchValue={''}
            handleSearch={handleSearch}
            filters={[...filters]}
            onFilterChange={handleFilterChange}
          />
          <Paper
            elevation={0}
            sx={{
              borderRadius: '4px',
              border: '1px solid #EAECF0',
              background: ' #FEFEFE',
              boxShadow:
                '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            }}
          >
            <TableContainer
              sx={{
                overflow: 'auto',
                maxHeight: openFilters ? '80vh' : '85vh',
                marginLeft: '2px',
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'lightgray transparent',
                  padding: '0px 4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: 'darkgray',
                  borderRadius: '10px',
                },
              }}
            >
              <Table stickyHeader>
                <CustomTableHeader
                  headLabel={header}
                  order={'desc'}
                  orderBy={''}
                  rowCount={events ? events.length : 0}
                  numSelected={0}
                />
                <TableBody>
                  {!loadingLogs &&
                    successLogs &&
                    events.map((event) => {
                      return (
                        <TableRow key={event.id}>
                          <TableCell>{event.event}</TableCell>
                          <TableCell>{event.eventSource}</TableCell>
                          <TableCell>
                            {dayjs(event.eventDate).format(
                              'MMMM D, YYYY HH:mm:ss'
                            )}
                          </TableCell>
                        </TableRow>
                      )
                    })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Stack>
      </Drawer>
    </>
  )
}

export default DeviceEventHistory
