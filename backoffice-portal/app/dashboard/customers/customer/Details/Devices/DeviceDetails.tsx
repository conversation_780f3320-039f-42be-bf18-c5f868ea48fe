import { ArrowBackRounded } from '@mui/icons-material'
import { Avatar, Button, Stack, Typography } from '@mui/material'
import { sentenceCase } from 'tiny-case'
import React from 'react'

import { DeviceStatusChange } from '@/app/dashboard/customers/customer/Details/Devices/MoreMenu'
import DeviceEventHistory from '@/app/dashboard/customers/customer/Details/Devices/DeviceEventHistory'
import { ReadOnlyTypography } from '@/app/components/ReadOnlyTypography'
import { setOpenDevice } from '@/app/redux/reducers/customers'
import { CustomerStatusChip } from '@/app/components/Chip'
import { DeviceIcons } from '@/app/components/SvgIcons/CustomerIcons'
import { useAppDispatch, useAppSelector } from '@/app/redux'

import LoadingDeviceDetails from './LoadingDeviceDetails'

export const DeviceDetail = () => {
  const dispatch = useAppDispatch()
  const { isLoadingDevice, device, customer } = useAppSelector(
    (state) => state.customers
  )
  return (
    <>
      {isLoadingDevice ? (
        <LoadingDeviceDetails />
      ) : (
        <Stack
          sx={{
            px: '1%',
            py: '1%',
          }}
        >
          <Stack
            sx={{
              width: '100%',
              px: '3%',
              pt: '1%',
              pb: '3%',
              background: '#FFFFFF',
              border: '1px solid #D0D5DD',
              borderRadius: '4px',
              gap: '4vh',
            }}
          >
            <Button
              startIcon={<ArrowBackRounded />}
              variant="outlined"
              sx={{
                width: '10%',
                height: '34px',
                padding: '1% 3%',
                gap: 0,
                borderRadius: '6px',
                border: '1px solid #AAADB0',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
            >
              <Typography
                sx={{
                  textWrap: 'nowrap',
                  textAlign: 'center',
                }}
                variant="label1"
                onClick={() => dispatch(setOpenDevice(false))}
              >
                All Devices
              </Typography>
            </Button>
            <Stack
              sx={{
                flexDirection: 'column',
                justifyContent: 'space-between',
                gap: '2vh',
              }}
            >
              <Stack
                sx={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    alignItems: 'center',
                    gap: '2vh',
                  }}
                >
                  <Avatar
                    sx={{
                      width: '50px',
                      height: '50px',
                      backgroundColor: '#E7E8E9',
                    }}
                  >
                    <DeviceIcons />
                  </Avatar>
                  <Stack
                    sx={{
                      flexDirection: 'column',
                      justifyContent: 'flex-start',
                      alignItems: 'flex-start',
                      gap: '4px',
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '20px',
                        color: '#000509',
                      }}
                      variant="subtitle1"
                    >
                      {device && device.deviceName}
                    </Typography>
                    <CustomerStatusChip label={device.deviceStatus} />
                  </Stack>
                </Stack>
                <Stack
                  sx={{
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    gap: '14px',
                  }}
                >
                  <DeviceEventHistory />
                  <DeviceStatusChange
                    device={device}
                    customer={customer}
                    origin={'view'}
                  />
                </Stack>
              </Stack>
              <Stack
                sx={{
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  alignContent: 'space-between',
                  gap: '3vh',
                }}
              >
                <Stack
                  sx={{
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    gap: '3vh',
                  }}
                >
                  <ReadOnlyTypography
                    label="Profile Device ID"
                    value={device.uuid}
                  />
                  <ReadOnlyTypography
                    label="Device Type"
                    value={device.deviceType}
                  />
                  <ReadOnlyTypography
                    label="Device Name"
                    value={device.deviceName}
                  />
                  <ReadOnlyTypography label="IMSI/UUID" value={device.uuid} />
                </Stack>
                <Stack
                  sx={{
                    justifyContent: 'space-between',
                    gap: '3vh',
                    flexDirection: 'row',
                  }}
                >
                  <ReadOnlyTypography
                    label="Device Status"
                    value={sentenceCase(device.deviceStatus)}
                  />
                  <ReadOnlyTypography
                    label="Device Model"
                    value={sentenceCase(device.deviceModel)}
                  />
                  <ReadOnlyTypography
                    label="Device Platform"
                    value={device.devicePlatform}
                  />
                  <ReadOnlyTypography
                    label="Phone Number"
                    value={device.phoneNumber}
                  />
                </Stack>
                <Stack
                  sx={{
                    justifyContent: 'space-between',
                    gap: '3vh',
                    flexDirection: 'row',
                    width: '38vw',
                  }}
                >
                  <ReadOnlyTypography
                    label="Date Created"
                    value={device.dateCreated}
                  />
                  <ReadOnlyTypography
                    label="Date Installed"
                    value={device.dateCreated}
                  />
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      )}
    </>
  )
}
