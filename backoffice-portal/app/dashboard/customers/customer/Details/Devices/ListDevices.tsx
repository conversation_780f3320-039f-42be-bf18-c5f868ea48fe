import {
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import dayjs from 'dayjs'
import React from 'react'

import { DeviceMoreMenu } from '@/app/dashboard/customers/customer/Details/Devices/MoreMenu'
import { CustomerStatusChip } from '@/app/components/Chip'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'
import { CustomPagination } from '@/app/components/Table/Pagination'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { RootState, useAppDispatch, useAppSelector } from '@/app/redux'
import { IHeadCell } from '@/app/interfaces/shared'
import {
  getCustomerDeviceDetail,
  getCustomerDevices,
} from '@/app/redux/actions/customers'
import { setOpenDevice } from '@/app/redux/reducers/customers'

import PageHeader from './pageHeader'
import EmptySearchAndFilter from './EmptySearchAndFilter'

const headers: IHeadCell[] = [
  {
    id: 'deviceId',
    label: 'Device ID',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceType',
    label: 'Device Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceName',
    label: 'Device Name',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceModel',
    label: 'Device Model',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'deviceStatus',
    label: 'Device Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'uuid',
    label: 'IMSI/UUID',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'dateCreated',
    label: 'Date Created',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'actions',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]

export const ListView = () => {
  const dispatch = useAppDispatch()

  const { devicesResponse, isSuccessDevices, isLoadingDevices, customer } =
    useAppSelector((state: RootState) => state.customers)
  const data = devicesResponse.data
  const handlePageClick = async (newPage: number) => {
    await getCustomerDevices({
      params: {
        profileID: customer.id ? customer.id : '',
        page: newPage,
        size: 7,
      },
      dispatch,
    })
  }
  const handleForwardClick = async () => {
    await getCustomerDevices({
      params: {
        profileID: customer.id ? customer.id : '',
        page: devicesResponse.pageNumber + 1,
        size: 7,
      },
      dispatch,
    })
  }
  const handleBackClick = async () => {
    await getCustomerDevices({
      params: {
        profileID: customer.id ? customer.id : '',
        page: devicesResponse.pageNumber - 1,
        size: 7,
      },
      dispatch,
    })
  }

  return (
    <Stack
      sx={{
        height: '100%',
        px: '3%',
        gap: '2vh',
      }}
    >
      <PageHeader />
      {isLoadingDevices ? (
        <CustomSkeleton variant="rectangular" width={'100%'} height={'60vh'} />
      ) : (
        <>
          {data && data.length === 0 ? (
            <EmptySearchAndFilter />
          ) : (
            <Paper
              elevation={0}
              sx={{
                boxShadow:
                  '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
                borderRadius: '4px',
                border: '1px solid #EAECF0',
                background: '#FFFFFF',
              }}
            >
              <TableContainer>
                <Table>
                  <CustomTableHeader
                    order={'desc'}
                    orderBy={''}
                    rowCount={0}
                    headLabel={[...headers]}
                    numSelected={0}
                  />
                  <TableBody>
                    {isSuccessDevices &&
                      data.map((row) => {
                        const {
                          deviceName,
                          deviceStatus,
                          deviceModel,
                          deviceType,
                          deviceId,
                          dateCreated,
                          uuid,
                        } = row
                        return (
                          <TableRow
                            key={uuid}
                            sx={{ cursor: 'pointer' }}
                            onClick={async () => {
                              if (!customer.isBlocked) {
                                dispatch(setOpenDevice(true))
                                await getCustomerDeviceDetail({
                                  deviceID: uuid,
                                  profileID: customer.id ? customer.id : '',
                                  dispatch: dispatch,
                                })
                              }
                            }}
                          >
                            <TableCell>{deviceId}</TableCell>
                            <TableCell>{deviceType}</TableCell>
                            <TableCell>{deviceName}</TableCell>
                            <TableCell>{deviceModel}</TableCell>
                            <TableCell>
                              <CustomerStatusChip label={deviceStatus} />
                            </TableCell>
                            <TableCell>{uuid}</TableCell>
                            <TableCell>
                              {dayjs(dateCreated).format('MMMM D, YYYY')}
                            </TableCell>
                            <TableCell
                              sx={{
                                padding: '8px',
                              }}
                            >
                              <DeviceMoreMenu device={row} />
                            </TableCell>
                          </TableRow>
                        )
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
              <CustomPagination
                pageCount={devicesResponse.totalNumberOfPages}
                page={devicesResponse.pageNumber}
                handlePageClick={handlePageClick}
                handleForwardClick={handleForwardClick}
                handleBackClick={handleBackClick}
              />
            </Paper>
          )}
        </>
      )}
    </Stack>
  )
}
