'use client'
import { Stack } from '@mui/material'
import React, { useState } from 'react'

import { CustomerModuleSearchFilterBox } from '@/app/components/Input/CustomerModuleSearchFilter'
import { IFilter } from '@/app/interfaces/shared'
import { getCustomerDevices } from '@/app/redux/actions/customers'
import { useAppDispatch, useAppSelector } from '@/app/redux'
const searchByItems: Array<{
  label: string
  value: string
}> = [
  {
    label: 'Device Type',
    value: 'deviceType',
  },
  {
    label: 'Device Status',
    value: 'deviceStatus',
  },
]

const PageHeader = () => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [searchByValue, setSearchByValue] = useState<string>('deviceType')

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
    const status =
      searchByValue === 'deviceStatus'
        ? search === 'Active'
          ? 'ACTIVE'
          : 'INACTIVE'
        : undefined
    try {
      await getCustomerDevices({
        dispatch,
        params: {
          profileID: customer.id ? customer.id : '',
          page: 0,
          size: 7,
          [`${searchByValue}`]: search,
          ...(status && { status }),
        },
      })
    } catch (error) {
      console.error('Error fetching customer devices:', error)
    }
  }

  const filters: IFilter[] = [
    {
      filterName: 'Device Type',
      options: [
        { key: 'App', value: 'App', label: 'App' },
        { key: 'Ussd', value: 'Ussd', label: 'USSD' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Device Status',
      options: [
        { key: 'ACTIVE', value: 'ACTIVE', label: 'Active' },
        { key: 'INACTIVE', value: 'INACTIVE', label: 'Inactive' },
      ],
      type: 'dropdown/single',
    },
  ]
  const handleFilterChange = async (
    filters: Record<string, string | string[]>
  ) => {
    const status = filters['Device Status'] as string
    const deviceType = filters['Device Type'] as string
    await getCustomerDevices({
      dispatch,
      params: {
        profileID: customer.id ? customer.id : '',
        page: 0,
        size: 7,
        status: status,
        deviceType: deviceType,
      },
    })
  }

  return (
    <Stack
      sx={{
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: '16px',
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '16px',
          width: '100%',
        }}
        direction="row"
      >
        <CustomerModuleSearchFilterBox
          openFilter={openFilter}
          setOpenFilter={setOpenFilter}
          searchValue={searchValue}
          searchByValues={searchByItems}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
          setSearchByValue={setSearchByValue}
        />
      </Stack>
    </Stack>
  )
}

export default PageHeader
