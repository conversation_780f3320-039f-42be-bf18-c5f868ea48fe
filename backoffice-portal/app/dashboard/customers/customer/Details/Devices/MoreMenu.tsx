import { CheckCircleOutline, DoDisturbRounded } from '@mui/icons-material'
import CloseIcon from '@mui/icons-material/Close'
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import {
  Button,
  Checkbox,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'

import { CheckBoxIcon } from '@/app/components/SvgIcons/CheckBoxIcons'
import { CustomFormControlLabel } from '@/app/components/Input/CustomFormControlLabel'
import { CustomDialog } from '@/app/components/Dialogs/CustomDialog'
import { LoadingButton } from '@/app/components/Loading/LoadingButton'
import { ICustomer, IDevice } from '@/app/interfaces/customers'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  activateCustomerDevice,
  deactivateCustomerDevice,
  getCustomerDeviceDetail,
  getCustomerDevices,
  makerActivateCustomerDevice,
  makerDeactivateCustomerDevice,
} from '@/app/redux/actions/customers'
import { setOpenDevice } from '@/app/redux/reducers/customers'
import { HasAccessToRights } from '@/app/utils/AccessControlHelper'

export const DeviceMoreMenu = ({ device }: { device: IDevice }) => {
  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
  }
  const handleClose = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setAnchorEl(null)
  }
  const handleSeeMoreDetails = async () => {
    dispatch(setOpenDevice(true))
    await getCustomerDeviceDetail({
      deviceID: device.uuid,
      profileID: customer.id ? customer.id : '',
      dispatch: dispatch,
    })
  }

  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
        disabled={customer.isBlocked}
      >
        Actions
      </Button>
      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <MenuItem onClick={handleSeeMoreDetails}>View more details</MenuItem>
        <DeviceStatusChange
          origin={'list'}
          device={device}
          customer={customer}
        />
      </Menu>
    </>
  )
}

export const DeviceStatusChange = ({
  device,
  customer,
  origin,
}: {
  device: IDevice
  customer: ICustomer
  origin: string
}) => {
  const [open, setOpen] = useState(false)
  const [hasTouched, setHasTouched] = useState(false)
  const [error, setError] = useState<string>('')

  const dispatch = useAppDispatch()
  const { isLoadingDevice } = useAppSelector((state) => state.customers)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation()
    setOpen(true)
  }
  const handleClose = (
    event: React.MouseEvent<HTMLElement>,
    reason: string
  ) => {
    event.stopPropagation()
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleDeviceStatusChange = async (
    event: React.MouseEvent<HTMLElement>
  ) => {
    event.stopPropagation()
    const profileID = customer.id
    if (!profileID) return

    const superAction =
      device.deviceStatus === 'ACTIVE'
        ? deactivateCustomerDevice
        : activateCustomerDevice
    const makerAction =
      device.deviceStatus === 'ACTIVE'
        ? makerDeactivateCustomerDevice
        : makerActivateCustomerDevice

    if (
      HasAccessToRights(['SUPER_DEACTIVATE_DEVICE', 'SUPER_ACTIVATE_DEVICE'])
    ) {
      await superAction({
        profileID,
        deviceID: device.uuid,
        dispatch,
        comments: reason,
      })
      handleClose(event, 'close')
    } else {
      await makerAction({
        profileID,
        deviceID: device.uuid,
        dispatch,
        comments: reason,
      })
    }
    await getCustomerDevices({
      params: {
        profileID,
        page: 0,
        size: 7,
      },
      dispatch,
    })
  }
  const deactivateReasons = [
    'Suspicious Activity',
    'Reported Security Breach',
    'Fraudulent Account',
    'Account Closure',
    'Changing Phones',
    'Other',
  ]
  const activateReasons = [
    'Device verification complete',
    'Device returned',
    'Device found',
    'Other',
  ]
  const [selectedReason, setSelectedReason] = useState<string>('')
  const [otherReason, setOtherReason] = useState<string>('')
  const reason = selectedReason === 'Other' ? otherReason : selectedReason

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation()
    const value = event.target.value
    setOtherReason(value)
    setHasTouched(true)

    if (!validateReason(value)) {
      setError(
        'Reason must be at least 10 characters and contain valid characters.'
      )
    } else {
      setError('')
    }
  }
  const validateReason = (reason: string) => {
    const trimmedReason = reason.trim()
    return (
      trimmedReason.length >= 10 && /^[a-zA-Z0-9\s.,!?'-]*$/.test(trimmedReason)
    )
  }
  const isButtonDisabled =
    selectedReason === '' ||
    (selectedReason === 'Other' &&
      (!otherReason || !validateReason(otherReason)))

  return (
    <>
      {origin === 'view' ? (
        <Button
          sx={{
            height: '34px',
            border: '1px solid #D0D5DD;',
            textWrap: 'nowrap',
            fontSize: '15px',
            gap: '2px',
            fontWeight: 500,
            '&.MuiButton-endIcon': {
              margin: 0,
            },
          }}
          variant="contained"
          endIcon={
            device.deviceStatus === 'ACTIVE' ? (
              <DoDisturbRounded fontSize="small" />
            ) : (
              <CheckCircleOutline fontSize="small" />
            )
          }
          onClick={handleClick}
          disabled={customer.isBlocked}
        >
          {device.deviceStatus === 'ACTIVE'
            ? 'Deactivate Device'
            : 'Activate Device'}
        </Button>
      ) : (
        <MenuItem onClick={handleClick}>
          {device.deviceStatus === 'ACTIVE' ? 'Deactivate' : 'Activate'}
        </MenuItem>
      )}
      <CustomDialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <Stack
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '16px',
              fontWeight: 600,
              py: '1%',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {device.deviceStatus === 'ACTIVE'
              ? 'Deactivate Device'
              : 'Activate Device'}
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 4,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <DialogContent
          sx={{
            px: '5% !important',
            gap: '2vh',
          }}
        >
          <Typography
            variant="body1"
            sx={{
              textWrap: 'wrap',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {`Please select a reason for ${device.deviceStatus === 'ACTIVE' ? 'deactivating' : 'activating'} this device.`}
          </Typography>
          <Stack gap={'1vh'}>
            {selectedReason !== 'Other' &&
              (device.deviceStatus === 'ACTIVE'
                ? deactivateReasons
                : activateReasons
              ).map((reason, index) => (
                <FormControl fullWidth key={index}>
                  <CustomFormControlLabel
                    control={<Checkbox icon={<CheckBoxIcon />} />}
                    onChange={(e) => {
                      e.stopPropagation()
                      setSelectedReason(reason)
                      setOtherReason('')
                    }}
                    onClick={(e) => {
                      e.stopPropagation()
                    }}
                    checked={selectedReason === reason}
                    label={reason}
                    labelPlacement={'start'}
                  />
                </FormControl>
              ))}
            {selectedReason === 'Other' && (
              <TextField
                rows={4}
                placeholder="Type your reason here"
                fullWidth
                onChange={handleChange}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={(e) => {
                  if (e.key) {
                    e.stopPropagation()
                  }
                }}
                error={Boolean(hasTouched && error)}
                helperText={hasTouched && error ? error : ''}
              />
            )}
          </Stack>
          <DialogActions
            sx={{
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}
          >
            <Button
              fullWidth
              variant="outlined"
              onClick={(e) => {
                handleClose(e, 'close')
              }}
            >
              Cancel
            </Button>
            {isLoadingDevice ? (
              <LoadingButton />
            ) : (
              <Button
                variant="contained"
                sx={{
                  background: `${device.deviceStatus === 'ACTIVE' ? '#EB0045' : 'text.primary'}`,
                  border: `1.5px solid ${device.deviceStatus === 'ACTIVE' ? '#EB0045' : 'text.primary'}`,
                  '&:hover': {
                    background: `${device.deviceStatus === 'ACTIVE' ? '#EB0045' : 'text.primary'}`,
                  },
                }}
                fullWidth
                onClick={(e) => handleDeviceStatusChange(e)}
                disabled={isButtonDisabled}
              >
                {device.deviceStatus === 'ACTIVE' ? 'Deactivate' : 'Activate'}
              </Button>
            )}
          </DialogActions>
        </DialogContent>
      </CustomDialog>
    </>
  )
}
