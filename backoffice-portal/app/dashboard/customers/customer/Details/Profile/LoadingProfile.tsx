import { Stack } from '@mui/material'
import React from 'react'

import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'

const LoadingProfile = () => {
  const skeletons = Array.from({ length: 9 }, (_, index) => (
    <CustomSkeleton key={index} />
  ))
  return (
    <Stack sx={{ py: '1%', px: '2%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFF',
          padding: '10px',
          border: '1px solid #eff0f0',
        }}
        direction="column"
        spacing={2}
      >
        {/* header loader */}
        <CustomSkeleton
          sx={{
            width: '150px',
            height: '40px',
          }}
          variant="rectangular"
        />
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Stack
            sx={{
              width: '400px',
              height: '100px',
              flexDirection: 'row',
              justifyContent: 'flex-start',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <CustomSkeleton
              sx={{
                width: '50px',
                height: '50px',
              }}
              variant="rounded"
            />
            <Stack>
              <CustomSkeleton
                sx={{
                  width: '150px',
                  height: '20px',
                }}
              />
              <CustomSkeleton
                sx={{
                  width: '150px',
                  height: '20px',
                }}
              />
            </Stack>
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '10px',
            }}
          >
            <CustomSkeleton
              sx={{
                width: '100px',
                height: '46px',
              }}
            />
            <CustomSkeleton
              sx={{
                width: '100px',
                height: '46px',
              }}
            />
          </Stack>
        </Stack>
        <Stack
          sx={{
            height: '250px',
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '5px',
          }}
        >
          {skeletons}
        </Stack>
      </Stack>
    </Stack>
  )
}

export default LoadingProfile
