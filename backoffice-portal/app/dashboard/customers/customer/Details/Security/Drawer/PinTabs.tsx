'use client'
import { useEffect, useState } from 'react'
import { Stack } from '@mui/material'
import { SearchRounded } from '@mui/icons-material'

import {
  CustomAntTab,
  CustomTabPanel,
  CustomToggleTabs,
} from '@/app/components/CustomToggle/CustomToggleTab'
import { CustomSearchInput } from '@/app/components/Input/CustomSearchInput'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  getCustomerPinHistory,
  getCustomerPinLogsBackoffice,
} from '@/app/redux/actions/customers'

import BackOfficePinHistory from './BackOfficePinHistory'
import CustomerPinHistory from './CustomerPinHistory'

const PinTabs = () => {
  const [tabValue, setTabValue] = useState(0)
  const [page, setPage] = useState(1)
  const size = 10

  const dispatch = useAppDispatch()
  const { customer } = useAppSelector((state) => state.customers)

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
    setPage(1)
  }

  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }
  useEffect(() => {
    if (tabValue === 0) {
      getCustomerPinLogsBackoffice(
        customer.id ? customer.id : '',
        dispatch,
        page,
        size
      )
    } else {
      getCustomerPinHistory({
        profileID: customer && customer.id,
        dispatch,
        page,
        size,
        pinType: 'PIN',
      })
    }
  }, [tabValue, page])
  return (
    <>
      <Stack
        direction={'row'}
        sx={{ justifyContent: 'space-between', alignItems: 'center' }}
      >
        <CustomSearchInput
          sx={{
            width: '40%',
            '&.Mui-focused': {
              width: '40%',
            },
          }}
          startAdornment={<SearchRounded />}
          placeholder="Search Event"
          // onChange={handleSearch}
          // value={search}
        />
        <CustomToggleTabs
          value={tabValue}
          onChange={handleChange}
          aria-label="ant example"
          // centered
          // variant="fullWidth"
          sx={{
            marginBottom: '1.5%',
            width: '30%',
          }}
        >
          <CustomAntTab label={`Back Office`} />
          <CustomAntTab label={`Customer`} />
        </CustomToggleTabs>
      </Stack>

      <CustomTabPanel value={tabValue} index={0}>
        <BackOfficePinHistory page={page} onPageChange={handlePageChange} />
      </CustomTabPanel>
      <CustomTabPanel value={tabValue} index={1}>
        <CustomerPinHistory page={page} onPageChange={handlePageChange} />
      </CustomTabPanel>
    </>
  )
}

export default PinTabs
