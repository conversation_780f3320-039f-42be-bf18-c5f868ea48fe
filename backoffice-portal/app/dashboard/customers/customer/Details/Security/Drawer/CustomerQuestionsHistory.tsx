import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from '@mui/material'
import dayjs from 'dayjs'

import { IHeadCell } from '@/app/interfaces/shared'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { useAppSelector } from '@/app/redux'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'
import { CustomPagination } from '@/app/components/Table/Pagination'

import { PinHistoryEmptyState } from './EmptyState'

const header: IHeadCell[] = [
  {
    id: 'event',
    label: 'Event Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'eventTimestamp',
    label: 'Event Timestamp',
    alignCenter: false,
    alignRight: false,
  },
]

const CustomerQuestionsHistory: React.FC<{
  page: number
  onPageChange: (newPage: number) => void
}> = ({ page, onPageChange }) => {
  const { customerPinLogResponse, isLoadingSecurity } = useAppSelector(
    (state) => state.customers
  )

  const handlePageClick = (newPage: number) => {
    onPageChange(newPage)
  }

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <TableContainer
        sx={{
          maxHeight: '83vh',
          overflowX: 'auto',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'lightgray transparent',
            padding: '0px 4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'darkgray',
            borderRadius: '10px',
          },
        }}
      >
        {!isLoadingSecurity ? (
          customerPinLogResponse?.data?.length > 0 ? (
            <Table stickyHeader>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={[...header]}
                numSelected={0}
              />
              <TableBody>
                {customerPinLogResponse.data.map((row) => (
                  <TableRow key={row?.id}>
                    <TableCell>{row?.event}</TableCell>
                    <TableCell>
                      {dayjs(row?.eventDate).format('MMMM D, YYYY hh:mm A')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <PinHistoryEmptyState />
          )
        ) : (
          <CustomSkeleton
            variant="rectangular"
            animation="wave"
            sx={{
              height: '70vh',
              width: '100%',
            }}
          />
        )}
      </TableContainer>
      {customerPinLogResponse?.data?.length > 0 && (
        <CustomPagination
          pageCount={customerPinLogResponse.totalNumberOfPages}
          page={page}
          handlePageClick={handlePageClick}
          handleForwardClick={() => handlePageClick(page + 1)}
          handleBackClick={() => handlePageClick(page - 1)}
          key={'pagination_pending_requests'}
        />
      )}
    </Paper>
  )
}

export default CustomerQuestionsHistory
