import { Stack, Typography } from '@mui/material'
import React from 'react'

export const PinHistoryEmptyState = () => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        py: '30vh',
        backgroundImage: "url('/background-empty.svg')",
        backgroundSize: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'top',
      }}
    >
      <Stack
        sx={{
          px: '5%',
          gap: '10px',
          justifyContent: 'center',
        }}
      >
        <Typography variant="h6">
          {"Seems like you don't have any history"}
        </Typography>
        {/* <Typography
          variant="body1"
          sx={{
            px: '10%',
            textAlign: 'center',
          }}
        >
          You can do this by clicking on the button below to link an account.
        </Typography> */}
        {/* <LinkAccountDialog /> */}
      </Stack>
    </Stack>
  )
}
