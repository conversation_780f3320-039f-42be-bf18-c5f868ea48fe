import { CloseRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Drawer,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'

import SecurityQuestionHistory from './SecurityQuestionHistory'

const SecurityDrawer = () => {
  const [open, setOpen] = React.useState(false)
  return (
    <>
      <Button
        variant={'outlined'}
        onClick={() => setOpen((prev) => !prev)}
        sx={{
          width: '100%',
          height: '36px',
          border: '1px solid #D0D5DD',
        }}
      >
        Security History
      </Button>
      <Drawer
        open={open}
        anchor="right"
        variant="persistent"
        PaperProps={{
          sx: {
            maxWidth: '72%',
            minWidth: '30%',
          },
        }}
      >
        <Paper
          elevation={0}
          sx={{
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              minWidth: '52vw',
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography variant="h6">Security Questions History</Typography>
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          <Stack>
            <SecurityQuestionHistory />
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}

export default SecurityDrawer
