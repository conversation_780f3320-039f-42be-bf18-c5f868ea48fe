import { Ava<PERSON>, <PERSON>, But<PERSON>, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import dayjs from 'dayjs'

import {
  CheckIcon,
  KeyIcon,
  LockIcon,
  XIcon,
} from '@/app/components/SvgIcons/SecurityIcons'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { getCustomerPinDetails } from '@/app/redux/actions/customers'
import { ReadOnlyTypography } from '@/app/components/ReadOnlyTypography'

import { ResetPin } from './ResetPin'
import PinDrawer from './Drawer/PIN'
import SecurityDrawer from './Drawer/Security'

const SecurityQuestions = () => {
  const { customer, customerPinDetails } = useAppSelector(
    (state) => state.customers
  )

  const dispatch = useAppDispatch()
  const [openResetPin, setOpenResetPin] = React.useState<boolean>(false)

  useEffect(() => {
    getCustomerPinDetails({ profileID: customer.id, dispatch })
  }, [customer.id, dispatch])

  const getDetailByType = (type: string) => {
    if (Array.isArray(customerPinDetails)) {
      return customerPinDetails.find((detail) => detail.type === type)
    }
    return null
  }

  const pinDetails = getDetailByType('PIN')
  const securityQuestionDetails = getDetailByType('Security Questions')
  return (
    <Stack sx={{ px: '3%', py: '1%', flexDirection: 'column', gap: '5vh' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          border: '1px solid #D0D5DD',
          px: '2%',
          py: '3%',
          gap: '5vh',
          justifyContent: '',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '10px',
              alignItems: 'flex-start',
            }}
          >
            <ResetPin open={openResetPin} setOpen={setOpenResetPin} />
            <Avatar sx={{ bgcolor: '#E7E8E9' }}>
              <KeyIcon />
            </Avatar>
            <Stack sx={{ flexDirection: 'column' }}>
              <Typography>PIN</Typography>
              <Stack
                sx={{ flexDirection: 'row', alignItems: 'center', gap: '4px' }}
              >
                <Box
                  sx={{
                    border: '1px solid #FECDCA',
                    borderRadius: '100%',
                    background: '#FEF3F2',
                    height: '19px',
                    width: '19px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {' '}
                  {pinDetails?.status === 'Active PIN' ? (
                    <CheckIcon />
                  ) : (
                    <XIcon />
                  )}
                </Box>

                <Typography
                  sx={{
                    color:
                      pinDetails?.status === 'Active PIN'
                        ? '#027A48'
                        : '#FA727B',
                    fontWeight: 500,
                    fontSize: 14,
                  }}
                >
                  {pinDetails?.status || 'Not Set'}
                </Typography>
              </Stack>
            </Stack>
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '8px',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              variant="contained"
              onClick={() => {
                setOpenResetPin(true)
              }}
              sx={{ height: '35px' }}
              disabled={customer.isBlocked}
            >
              Reset{' '}
            </Button>
            <Stack>
              <PinDrawer />
            </Stack>
          </Stack>
        </Stack>
        <Stack
          sx={{ flexDirection: 'row', justifyContent: 'space-between' }}
          direction="row"
          spacing={2}
        >
          <ReadOnlyTypography
            label="Pin Status"
            value={pinDetails?.status || 'Not Set'}
          />

          <ReadOnlyTypography
            label="Pin Retries"
            value={pinDetails?.attempts || 'N/A'}
          />

          <ReadOnlyTypography
            label="Date First Created"
            value={dayjs(pinDetails?.dateFirstCreated).format(
              'MMMM D, YYYY h:mm A'
            )}
          />

          <ReadOnlyTypography
            label="Date Last Changed"
            value={
              customerPinDetails?.[1]?.dateLastChanged
                ? dayjs(securityQuestionDetails?.dateLastChanged).format(
                    'MMMM D, YYYY h:mm A'
                  )
                : 'N/A'
            }
          />
        </Stack>
      </Stack>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          border: '1px solid #D0D5DD',
          px: '2%',
          py: '3%',
          gap: '5vh',
        }}
      >
        <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          <Stack
            sx={{ flexDirection: 'row', gap: '10px', alignItems: 'flex-start' }}
          >
            <Avatar sx={{ bgcolor: '#E7E8E9' }}>
              <LockIcon />
            </Avatar>
            <Stack sx={{ flexDirection: 'column' }}>
              <Typography>Security Questions</Typography>
              <Stack sx={{ flexDirection: 'row' }}>
                <Stack
                  sx={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    gap: '5px',
                  }}
                >
                  <Box
                    sx={{
                      border: '1px solid #FECDCA',
                      borderRadius: '100%',
                      background: '#FEF3F2',
                      height: '19px',
                      width: '19px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                  >
                    {securityQuestionDetails?.status === 'Active Set' ? (
                      <CheckIcon />
                    ) : (
                      <XIcon />
                    )}
                  </Box>
                  <Typography
                    sx={{
                      color:
                        securityQuestionDetails?.status === 'Active Set'
                          ? '#027A48'
                          : '#FA727B',
                      fontWeight: 500,
                      fontSize: 14,
                    }}
                  >
                    {securityQuestionDetails?.status || 'Not Set'}
                  </Typography>
                </Stack>
              </Stack>
            </Stack>
          </Stack>

          {/* <Button
            variant="outlined"
            sx={{ border: '1px solid #D0D5DD', height: '35px' }}
            onClick={() => {
              dispatch(
                setDrawer({
                  open: true,
                  header: 'Security Questions History',
                  drawerChildren: {
                    childType: 'security_question_history',
                    data: [],
                  },
                })
              )
            }}
            disabled={customer.isBlocked}
          >
            Security Questions History
          </Button> */}
          <Stack>
            <SecurityDrawer />
          </Stack>
        </Stack>
        <Stack
          sx={{ flexDirection: 'row', justifyContent: 'space-between' }}
          direction="row"
          spacing={2}
        >
          <ReadOnlyTypography
            label="Security Questions Status"
            value={securityQuestionDetails?.status || 'Not Set'}
          />
          <ReadOnlyTypography
            label="Security Questions Retries"
            value={securityQuestionDetails?.attempts || 'N/A'}
          />
          <ReadOnlyTypography
            label="Date First Created"
            value={
              customerPinDetails?.[0]?.dateFirstCreated
                ? dayjs(securityQuestionDetails?.dateFirstCreated).format(
                    'MMMM D, YYYY h:mm A'
                  )
                : 'N/A'
            }
          />
          <ReadOnlyTypography
            label="Date Last Changed"
            value={
              customerPinDetails?.[0]?.dateLastChanged
                ? dayjs(securityQuestionDetails?.dateLastChanged).format(
                    'MMMM D, YYYY h:mm A'
                  )
                : 'N/A'
            }
          />
        </Stack>
      </Stack>
    </Stack>
  )
}
export default SecurityQuestions
