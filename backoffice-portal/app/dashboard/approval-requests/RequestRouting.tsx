'use client'

import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'

import { setSelectedApprovalRequest } from '@/app/redux/reducers/ApprovalRequests'
import { setNotification } from '@/app/redux/reducers/notifications'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { ICustomer, ICustomerAccount } from '@/app/interfaces/customers'
import {
  getCustomerAccountByAccountNo,
  getCustomerDeviceDetail,
  getCustomerPinDetails,
  getCustomerProfile,
} from '@/app/redux/actions/customers'
import { getRoleById } from '@/app/redux/actions/roles'
import { getUserById } from '@/app/redux/actions/staffUsers'
import {
  setChangeTab,
  setCustomer,
  setCustomerAccountsList,
  setCustomerApprovalBarOpen,
  setIsViewAccountOpen,
  setOpenDevice,
} from '@/app/redux/reducers/customers'
import { setSwitchToRoleDetails } from '@/app/redux/reducers/navigation'
import {
  extractFields,
  isAccountLinkingApprovalRequest,
} from '@/app/utils/helpers'

export const ApprovalRequestRouting = async (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[request.makerCheckerType.type]
      if (subHandler) {
        await subHandler(request, dispatch, router)
      }
    }
  }
}
type Handler = (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}

const handlers: Handlers = {
  users: async (request, dispatch, router) => {
    if (request.entityId) await getUserById(dispatch, request.entityId)
    dispatch(setSelectedApprovalRequest(request))
    router.push('/dashboard/staff-users/details')
  },
  groups: async (request, dispatch, router) => {
    if (request.entityId) await getRoleById(dispatch, request.entityId)
    dispatch(setSwitchToRoleDetails({ open: true, role: {}, type: 'approve' }))
    dispatch(setSelectedApprovalRequest(request))
    router.push('/dashboard/staff-users/details')
  },
  Customers: {
    CREATE_CUSTOMERS: async (request, dispatch, router) => {
      const customer: ICustomer = {
        id: extractFields('entityId', request) as string,
        country: extractFields('country', request) as string,
        firstName: extractFields('firstName', request) as string,
        otherNames: '',
        lastName: extractFields('lastName', request) as string,
        phoneNumber: extractFields('phoneNumber', request) as string,
        email: extractFields('email', request) as string,
        nationality: extractFields('nationality', request) as string,
        idNumber: extractFields('idValue', request) as string,
        idType: extractFields('idType', request) as string,
        sex: extractFields('gender', request) as string,
        onboardingType: '',
        isBlocked: true,
        dateCreated: request.dateCreated,
        dateModified: request.dateModified,
        storeOfValues: [],
        blockReason: '',
        profileAccountStoreIds: [],
      }
      dispatch(setCustomer(customer))
      const accounts = request.diff.find(
        (change) => change.field === 'customerAccounts'
      )?.newValue
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-expect-error
      dispatch(setCustomerAccountsList(accounts as ICustomerAccount[]))

      dispatch(setSelectedApprovalRequest(request))
      dispatch(setCustomerApprovalBarOpen(true))
      router.push(`/dashboard/customers/customer`)
      dispatch(setChangeTab(0))
    },
    UPDATE_CUSTOMERS: async (request, dispatch, router) => {
      dispatch(setSelectedApprovalRequest(request))
      const customer = await getCustomerProfile(
        request.entityId || '',
        dispatch
      )
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        if (isAccountLinkingApprovalRequest(request.entity || '')) {
          router.push(`/dashboard/customers/customer`)
          dispatch(setChangeTab(3))
        } else {
          router.push(`/dashboard/customers/customer`)
          dispatch(setChangeTab(0))
        }
      }
    },
    ACTIVATE_CUSTOMERS: async (request, dispatch, router) => {
      const customerId = JSON.parse(request.entity as string).id
        ? JSON.parse(request.entity as string).id
        : request.entityId
      const customer = await getCustomerProfile(customerId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setSelectedApprovalRequest(request))
        dispatch(setChangeTab(0))
      }
    },
    DEACTIVATE_CUSTOMERS: async (request, dispatch, router) => {
      const customerId = JSON.parse(request.entity as string).id
        ? JSON.parse(request.entity as string).id
        : request.entityId
      await getCustomerProfile(customerId, dispatch)
      dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
      router.push(`/dashboard/customers/customer`)
      dispatch(setSelectedApprovalRequest(request))
      dispatch(setChangeTab(0))
    },
    DELETE_CUSTOMERS: async (request, dispatch, router) => {
      const profileId = request.entityId ? request.entityId : ''
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(0))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
  },
  ProfileDevices: {
    DEACTIVATE_PROFILEDEVICES: async (request, dispatch, router) => {
      const profile = JSON.parse(request.entity ?? '{}')
      const customer = await getCustomerProfile(
        profile.profileId || '',
        dispatch
      )
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerDeviceDetail({
          profileID: profile.profileId,
          deviceID: request.entityId || '',
          dispatch,
        })
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(2))
        dispatch(setOpenDevice(true))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    ACTIVATE_PROFILEDEVICES: async (request, dispatch, router) => {
      const profile = JSON.parse(request.entity ?? '{}')
      const customer = await getCustomerProfile(
        profile.profileId || '',
        dispatch
      )
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerDeviceDetail({
          profileID: profile.profileId,
          deviceID: request.entityId || '',
          dispatch,
        })
        dispatch(setCustomer(customer))
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(2))
        dispatch(setOpenDevice(true))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    // Add other cases as needed
  },
  Profiles: {
    UPDATE_PROFILES: async (request, dispatch, router) => {
      const profile = JSON.parse(request.entity ?? '{}')
      const customer = await getCustomerProfile(
        profile.profileId || '',
        dispatch
      )
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerPinDetails({
          profileID: profile.profileId || '',
          dispatch,
        })
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setSelectedApprovalRequest(request))
        dispatch(setChangeTab(5))
      }
    },
  },
  accounts: {
    DEACTIVATE_ACCOUNTS: async (request, dispatch, router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerAccountByAccountNo(
          profileId,
          request.entityId || '',
          dispatch
        )
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        dispatch(setIsViewAccountOpen(true))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(3))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    ACTIVATE_ACCOUNTS: async (request, dispatch, router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerAccountByAccountNo(
          profileId,
          request.entityId || '',
          dispatch
        )
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        dispatch(setIsViewAccountOpen(true))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(3))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    RESTRICT_ACCOUNTS: async (request, dispatch, router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        await getCustomerAccountByAccountNo(
          profileId,
          request.entityId || '',
          dispatch
        )
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        dispatch(setIsViewAccountOpen(true))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(3))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    CREATE_ACCOUNTS: async (request, dispatch, router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(3))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
    DELETE_ACCOUNTS: async (request, dispatch, router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      const customer = await getCustomerProfile(profileId, dispatch)
      if (!customer) {
        dispatch(
          setNotification({
            message: 'Customer Profile not found',
            type: 'warning',
          })
        )
        return
      } else {
        dispatch(setCustomerApprovalBarOpen(request.status === 'PENDING'))
        router.push(`/dashboard/customers/customer`)
        dispatch(setChangeTab(3))
        dispatch(setSelectedApprovalRequest(request))
      }
    },
  },
}
