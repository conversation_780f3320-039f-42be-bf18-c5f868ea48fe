import { CloseRounded, FilterListRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  ChipProps,
  Paper,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { sentenceCase } from 'tiny-case'
import React, { useEffect } from 'react'
import dayjs from 'dayjs'

import { PendingRequestMoreMenu } from '@/app/dashboard/approval-requests/Pending/MoreMenu'
import { CustomPagination } from '@/app/components/Table/Pagination'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { IHeadCell } from '@/app/interfaces/shared'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import {
  getApprovalRequestTypes,
  getApprovals,
} from '@/app/redux/actions/approvalRequests'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import DateRangePicker from '@/app/components/DropDownMenus/DateRangePicker'
import { DropDownMenu } from '@/app/components/DropDownMenus/Index'

import RequestSearch from '../RequestSearch'

export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0',
}))

export const CustomTableCell = styled(TableCell)(() => ({
  padding: '16px 24px',
}))

const pendingRequest: IHeadCell[] = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker_timestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'action',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]
type ExtendedKeys =
  | keyof IApprovalRequest
  | keyof IApprovalRequest['makerCheckerType']
export const requestSearchByItems: {
  label: string
  value: Array<ExtendedKeys>
}[] = [
  // { label: 'Request Type', value: ['type'] },
  // {label: 'Maker' , value: ['maker']},
  { label: 'Module', value: ['module'] },
]

const Pending = () => {
  const dispatch = useAppDispatch()

  const [page, setPage] = React.useState(1)
  const [openFilterBar, setOpeFilterBar] = React.useState<boolean>(false)

  // filters

  const [dateRange, setDateRange] = React.useState<{
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null>(null)
  const [makerName, setMakerName] = React.useState<string>('')

  const handlePageClick = (newPage: number) => {
    setPage(newPage)
  }

  const handleForwardClick = () => {
    setPage(page + 1)
  }

  const handleBackwardClick = () => {
    setPage(page - 1)
  }

  const {
    approvalRequestResponse,
    isLoadingRequests,
    approvalRequests,
    requestTypes,
    isRequestTypesLoading,
    isRequestTypesSuccess,
  } = useAppSelector((state) => state.approvalRequests)

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const handleDateRangeFilterApply = (date: {
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  }) => {
    setDateRange(date)
    let params = `?status=${'PENDING'}&channel=${'DBP'}&createDateFrom=${date.start.format('YYYY-MM-DD')}&createDateTo=${date.end.format('YYYY-MM-DD')}&page=${page}&size=10`
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''

    getApprovals(dispatch, params)
  }

  const handleRequestTypeFilter = (requestTypeId: string) => {
    let params = `?status=${'PENDING'}&channel=${'DBP'}&requestType=${requestTypeId}&page=${page}&size=10`
    params += dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''
    getApprovals(dispatch, params)
  }

  useEffect(() => {
    const limit = 10
    const params = `?status=${'PENDING'}&channel=${'DBP'}&size=${limit}&page=${page}`
    getApprovals(dispatch, params)
  }, [page])

  return (
    <Box
      sx={{
        padding: '2% 2% 0 2.5%',
        display: 'flex',
        flexDirection: 'column',
        gap: '22px',
      }}
    >
      {/* search and filter */}

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          gap: '16px',
          flexDirection: 'column',
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
              getApprovals(
                dispatch,
                `?status=${'PENDING'}&channel=${'DBP'}&${makerNameFilter()}=${makerName}&size=10&page=0`
              )
            }}
          />
          <Button
            sx={{
              display: 'flex',
              width: '131px',
              padding: '8px 42px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              height: '42px',
              borderRadius: '4px',
              border: '1.5px solid #D0D5DD',
              background: '#FFF',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            variant="outlined"
            startIcon={<FilterListRounded />}
            onClick={() => setOpeFilterBar(!openFilterBar)}
          >
            Filter
          </Button>
        </Box>
        {openFilterBar && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-start',
              gap: '16px',
            }}
          >
            <Button
              onClick={() => {
                setDateRange(null)
                getApprovals(
                  dispatch,
                  `?status=${'PENDING'}&channel=${'DBP'}&size=10&page=0`
                )
              }}
              sx={{
                minWidth: '131px',
                height: '40px',
                gap: '0px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
              endIcon={
                <Typography
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <CloseRounded />
                </Typography>
              }
            >
              <Typography>Clear</Typography>
            </Button>
            <DateRangePicker
              onApplyDateRange={handleDateRangeFilterApply}
              buttonText="Date Created"
            />
            <DropDownMenu
              menuItems={
                isRequestTypesSuccess
                  ? [
                      ...requestTypes.map((item) => {
                        return { label: item.name, id: item.id }
                      }),
                    ]
                  : []
              }
              loading={isRequestTypesLoading}
              onSelect={handleRequestTypeFilter}
              buttonText={'Request type'}
              onButtonClick={(setOpen) => {
                setOpen((prev) => !prev)
                getApprovalRequestTypes(dispatch, 'DBP')
              }}
            />
          </Box>
        )}
      </Box>
      {/*<ReviewRequest />*/}
      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={pendingRequest}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.map((row, index) => (
                  <TableRow key={index || row.id}>
                    <CustomTableCell
                      sx={{
                        padding: '10px 24px 10px 16px',
                      }}
                    >
                      <Box>
                        <RequestChip
                          label={sentenceCase(row.makerCheckerType.name)}
                          sx={{ width: 'auto' }}
                        />
                      </Box>
                    </CustomTableCell>
                    <CustomTableCell>
                      {sentenceCase(row.makerCheckerType.module)}
                    </CustomTableCell>
                    <CustomTableCell>{row.maker}</CustomTableCell>
                    <CustomTableCell>
                      {dayjs(row.dateCreated).format('MMMM D, YYYY hh:mm A')}
                    </CustomTableCell>
                    <CustomTableCell sx={{ padding: 0 }}>
                      <PendingRequestMoreMenu request={row} />
                    </CustomTableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <CustomPagination
            pageCount={approvalRequestResponse.totalNumberOfPages}
            page={page}
            handlePageClick={handlePageClick}
            handleForwardClick={handleForwardClick}
            handleBackClick={handleBackwardClick}
          />
        </Paper>
      )}
    </Box>
  )
}

export default Pending
