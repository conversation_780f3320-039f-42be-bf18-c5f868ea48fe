import { CallMadeRounded, CloseRounded } from '@mui/icons-material'
import {
  But<PERSON>,
  Dialog,
  DialogContent,
  IconButton,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import dayjs from 'dayjs'

import { ApprovalRequestRouting } from '@/app/dashboard/approval-requests/RequestRouting'
import { handleDiff, useCustomRouter } from '@/app/utils/helpers'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { RequestsApprovalIcon } from '@/app/components/SvgIcons/SidebarIcons'
import { useAppDispatch } from '@/app/redux'
import AccessControlWrapper from '@/app/utils/AccessControlHelper'
import { ACCESS_CONTROLS } from '@/app/const'

const ReviewRequest = ({ request }: { request: IApprovalRequest }) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [open, setOpen] = useState<boolean>(false)

  const handleReviewRequest = async () => {
    await ApprovalRequestRouting(request, dispatch, router)
    setOpen(false)
  }
  const handleClose = (
    e: React.MouseEvent<HTMLButtonElement> | null,
    action: string
  ) => {
    if (action !== 'backdropClick') {
      setOpen(false)
    }
  }
  return (
    <>
      <MenuItem onClick={() => setOpen(!open)}>
        <Typography>See request summary</Typography>
      </MenuItem>
      <Dialog open={open}>
        <DialogContent
          sx={{
            padding: '12px 20px 7px 20px',
            width: '430px',
            display: 'flex',
            flexDirection: 'column',
            gap: '9px',
          }}
        >
          <Stack>
            {/*header icons */}
            <Stack
              sx={{
                justifyContent: 'space-between',
                alignItems: 'center',
                flexDirection: 'row',
              }}
            >
              <RequestsApprovalIcon />
              <IconButton onClick={(e) => handleClose(e, 'close')}>
                <CloseRounded />
              </IconButton>
            </Stack>
            <Typography
              variant="subtitle1"
              sx={{
                fontSize: '18px',
              }}
            >
              Approval request details
            </Typography>
          </Stack>
          <Stack
            sx={{
              gap: '20px',
            }}
          >
            <TextField
              fullWidth
              label="Approval request type"
              sx={{}}
              value={
                request?.makerCheckerType.type
                  ? sentenceCase(request?.makerCheckerType.type)
                  : ''
              }
            />
            <TextField
              fullWidth
              label="Changes made"
              sx={{
                width: 'auto',
              }}
              multiline
              value={handleDiff(request.diff)}
            />
            <TextField
              fullWidth
              label="Maker"
              sx={{}}
              value={request && request.maker}
            />
            <TextField
              fullWidth
              label="Maker timestamp"
              sx={{}}
              value={
                request && dayjs(request.dateCreated).format('MMMM D, YYYY')
              }
            />
            <TextField
              fullWidth
              label="Maker comment"
              sx={{}}
              value={
                request && request.makerComments
                  ? request.makerComments
                  : 'No comment'
              }
            />
          </Stack>
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '5%',
            }}
          >
            <Button
              variant="outlined"
              sx={{
                height: '34px',
              }}
              fullWidth
              onClick={(e) => handleClose(e, 'close')}
            >
              Back
            </Button>
            <AccessControlWrapper
              rights={[
                ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_CUSTOMERS,
                ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
                ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_CUSTOMERS,
                ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_ROLES,
                ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_USERS,
              ]}
            >
              <Button
                variant="contained"
                sx={{
                  height: '34px',
                  textWrap: 'nowrap',
                }}
                fullWidth
                onClick={handleReviewRequest}
                endIcon={<CallMadeRounded />}
              >
                Review Request
              </Button>
            </AccessControlWrapper>
          </Stack>
        </DialogContent>
      </Dialog>
    </>
  )
}

export default ReviewRequest
