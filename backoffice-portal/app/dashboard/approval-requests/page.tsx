'use client'
import React, { useEffect } from 'react'
import { Divider, Stack, Typography } from '@mui/material'

import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { useAppDispatch } from '@/app/redux'
import { setSelectedApprovalRequest } from '@/app/redux/reducers/ApprovalRequests'
import { AntTab, AntTabs, TabPanel } from '@/app/components/Tabs'
import { RequestsApprovalIcon } from '@/app/components/SvgIcons/SidebarIcons'

import Resolved from './All'
import Pending from './Pending'

const HomePage = () => {
  const [value, setValue] = React.useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const dispatch = useAppDispatch()
  useEffect(() => {
    dispatch(setSelectedApprovalRequest({} as IApprovalRequest))
  }, [])
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <RequestsApprovalIcon width="28" height="26" />
        <Typography variant="h5">Approval Request</Typography>
      </Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
        }}
        onChange={handleChange}
        value={value}
      >
        <AntTab label={`Pending Requests`} />
        <AntTab label={`All Requests`} />
      </AntTabs>
      <Divider />
      <TabPanel value={value} index={0}>
        <Pending />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <Resolved />
      </TabPanel>
    </Stack>
  )
}

export default HomePage
