'use client'

import { CloseRounded, FilterListRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import dayjs from 'dayjs'

import { AllApprovalRequestsMoreMenu } from '@/app/dashboard/approval-requests/All/MoreMenu'
import { CustomerStatusChip } from '@/app/components/Chip'
import { CustomPagination } from '@/app/components/Table/Pagination'
import { CustomTableHeader } from '@/app/components/Table/TableHead'
import { IHeadCell } from '@/app/interfaces/shared'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import {
  getApprovalRequestTypes,
  getApprovals,
} from '@/app/redux/actions/approvalRequests'
import { CustomSkeleton } from '@/app/components/Loading/CustomSkeleton'
import {
  DropDownMenu,
  DropDownMenuRadio,
} from '@/app/components/DropDownMenus/Index'
import DateRangePicker from '@/app/components/DropDownMenus/DateRangePicker'

import { CustomTableCell, RequestChip } from '../Pending'
import RequestSearch from '../RequestSearch'

const resolvedHeader: IHeadCell[] = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'action',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]

type ExtendedKeys =
  | keyof IApprovalRequest
  | keyof IApprovalRequest['makerCheckerType']
export const requestSearchByItems: {
  label: string
  value: Array<ExtendedKeys>
}[] = [
  // { label: 'Request Type', value: ['type'] },
  // {label: 'Maker' , value: ['maker']},
  { label: 'Module', value: ['module'] },
]

const Resolved: React.FC = () => {
  const dispatch = useAppDispatch()

  const [page, setPage] = useState(1)

  const [openFilterBar, setOpenFilterBar] = useState<boolean>(false)

  const [status, setStatus] = useState<string | null>('')
  const [requestType, setRequestType] = useState<string | null>('')

  const handlePageClick = (newPage: number) => {
    setPage(newPage)
  }

  const handleForwardClick = () => {
    setPage(page + 1)
  }

  const handleBackwardClick = () => {
    setPage(page - 1)
  }

  const {
    approvalRequests,
    isLoadingRequests,
    approvalRequestResponse,
    isRequestTypesSuccess,
    isRequestTypesLoading,
    requestTypes,
  } = useAppSelector((state) => state.approvalRequests)

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const handleDateRangeFilterApply = async (date: {
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  }) => {
    setDateRange(date)
    let params = `?channel=${'DBP'}&createDateFrom=${date.start.format('YYYY-MM-DD')}&createDateTo=${date.end.format('YYYY-MM-DD')}&page=${page}&size=7`
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += status ? `&status=${status}` : ''
    params += requestType ? `&requestType=${requestType}` : ''
    await getApprovals(dispatch, params)
  }

  const handleSearch = async (name: string) => {
    const limit = 10
    const params = `?channel=${'DBP'}&${makerNameFilter()}=${name}&size=${limit}&page=${page}`
    await getApprovals(dispatch, params)
  }

  // filters

  const [dateRange, setDateRange] = React.useState<{
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null>(null)
  const [makerName, setMakerName] = React.useState<string>('')

  useEffect(() => {
    getApprovals(dispatch, `?channel=${'DBP'}&size=10&page=${page}`)
  }, [page])

  const handleStatusFilter = async (item: string) => {
    setStatus(item)
    let params = `?channel=${'DBP'}&status=${item.toUpperCase()}&size=10&page=0`
    params += dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += requestType ? `&requestType=${requestType}` : ''

    await getApprovals(dispatch, params)
  }

  const handleRequestTypeFilter = async (requestTypeId: string) => {
    setRequestType(requestTypeId)
    let params = `?&channel=${'DBP'}&requestType=${requestTypeId}&page=${page}&size=10`
    params += dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += status ? `&status=${status.toUpperCase()}` : ''
    await getApprovals(dispatch, params)
  }
  return (
    <Box
      sx={{
        padding: '2% 2% 0 2.5%',
        display: 'flex',
        flexDirection: 'column',
        gap: '22px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          gap: '16px',
          flexDirection: 'column',
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
              handleSearch(makerName)
            }}
          />
          <Button
            sx={{
              display: 'flex',
              width: '131px',
              padding: '8px 42px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              height: '42px',
              borderRadius: '4px',
              border: '1.5px solid #D0D5DD',
              background: '#FFF',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            variant="outlined"
            startIcon={<FilterListRounded />}
            onClick={() => setOpenFilterBar(!openFilterBar)}
          >
            Filter
          </Button>
        </Box>
        {openFilterBar && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-start',
              gap: '16px',
            }}
          >
            <Button
              onClick={() => {
                setDateRange(null)
                setMakerName('')
                getApprovals(dispatch, `?channel=${'DBP'}&size=7&page=0`)
              }}
              sx={{
                minWidth: '131px',
                height: '40px',
                gap: '0px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
              endIcon={
                <Typography
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <CloseRounded />
                </Typography>
              }
            >
              <Typography>Clear</Typography>
            </Button>
            <DateRangePicker
              onApplyDateRange={handleDateRangeFilterApply}
              buttonText="Date Created"
            />

            <DropDownMenuRadio
              menuItems={['Approved', 'Pending', 'Rejected']}
              onClick={handleStatusFilter}
              buttonText={'Status'}
            />

            <DropDownMenu
              menuItems={
                isRequestTypesSuccess
                  ? [
                      ...requestTypes.map((item) => {
                        return { label: item.name, id: item.id }
                      }),
                    ]
                  : []
              }
              loading={isRequestTypesLoading}
              onSelect={handleRequestTypeFilter}
              buttonText={'Request type'}
              onButtonClick={async (setOpen) => {
                setOpen((prev) => !prev)
                await getApprovalRequestTypes(dispatch, 'DBP')
              }}
            />
          </Box>
        )}
      </Box>

      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={[...resolvedHeader]}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.map((request, index) => {
                  return (
                    <TableRow key={index || request.id}>
                      <CustomTableCell>
                        {' '}
                        <Box>
                          <RequestChip
                            label={sentenceCase(request.makerCheckerType.type)}
                            sx={{ width: 'auto' }}
                          />
                        </Box>
                      </CustomTableCell>
                      <CustomTableCell>
                        {sentenceCase(request.makerCheckerType.module)}
                      </CustomTableCell>
                      <CustomTableCell>
                        <CustomerStatusChip label={request.status} />
                      </CustomTableCell>
                      <CustomTableCell>{request.maker}</CustomTableCell>
                      <CustomTableCell>{request.checker}</CustomTableCell>
                      <CustomTableCell
                        sx={{
                          padding: '0px',
                        }}
                      >
                        <AllApprovalRequestsMoreMenu request={request} />
                      </CustomTableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </TableContainer>
          <CustomPagination
            pageCount={approvalRequestResponse?.totalNumberOfPages || 0}
            page={page}
            handlePageClick={handlePageClick}
            handleForwardClick={handleForwardClick}
            handleBackClick={handleBackwardClick}
          />
        </Paper>
      )}
    </Box>
  )
}

export default Resolved
