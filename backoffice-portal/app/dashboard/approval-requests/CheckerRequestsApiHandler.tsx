import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'

import {
  approveCreateRole,
  checkDeleteRole,
  checkUpdateRole,
  rejectCreateRole,
} from '@/app/redux/actions/roles'
import {
  activateUser,
  approveCreateUser,
  approveUpdateUser,
  deactivateUser,
  rejectCreateUser,
  rejectUpdateUser,
} from '@/app/redux/actions/staffUsers'
import { isAccountLinkingApprovalRequest } from '@/app/utils/helpers'
import {
  acceptActivateCustomerAccount,
  acceptActivateCustomerDevice,
  acceptCreateCustomer,
  acceptDeactivateCustomer,
  acceptDeactivateCustomerAccount,
  acceptDeactivateCustomerDevice,
  acceptRestrictAccount,
  activateCustomerProfileApprove,
  activateCustomerProfileReject,
  approveCustomerPinReset,
  approveDeleteCustomer,
  approveRejectAccountLinking,
  approveUnlinkCustomerAccount,
  approveUpdateCustomer,
  rejectActivateCustomerAccount,
  rejectCreateCustomer,
  rejectDeactivateCustomer,
  rejectDeactivateCustomerAccount,
  rejectDeactivateCustomerDevice,
  rejectRestrictAccount,
  rejectUnlinkCustomerAccount,
  rejectUpdateCustomer,
} from '@/app/redux/actions/customers'
import { IApprovalRequest } from '@/app/interfaces/approvalRequests'
import { setCustomerApprovalBarOpen } from '@/app/redux/reducers/customers'
//This function takes the action and approval request and makes the api call to the selected api call and routes to the final route
export const CheckerRequestsApiHandler = async (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  action: string,
  comments?: string
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      //TODO: Remove this check when backend separates account linking from profile update
      if (
        request.makerCheckerType.type === 'CREATE_ACCOUNTS' &&
        isAccountLinkingApprovalRequest(request.entity || '')
      ) {
        action = action.includes('REJECT')
          ? 'REJECT_CREATE_ACCOUNTS'
          : 'ACCEPT_CREATE_ACCOUNTS'
      }
      //remove end
      const subHandler = handler[action]
      if (subHandler) {
        await subHandler(request, dispatch, router, comments)
      }
    }
  }
}
type Handler = (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  comments?: string
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}
const handlers: Handlers = {
  Customers: {
    //accept actions
    ACCEPT_CREATE_CUSTOMERS: async (request, dispatch, router) => {
      await acceptCreateCustomer({
        approvalId: request.id,
        comments: 'Approve customer creation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_UPDATE_CUSTOMERS: async (request, dispatch, router) => {
      await approveUpdateCustomer({
        approvalID: request.id,
        comments: 'Update customer details',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_DEACTIVATE_CUSTOMERS: async (request, dispatch, router) => {
      await acceptDeactivateCustomer({
        approvalID: request.id,
        comments: 'Approve customer deactivation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_DELETE_CUSTOMERS: async (request, dispatch, router) => {
      await approveDeleteCustomer({
        approvalID: request.id,
        comments: 'Approve customer deletion',
        type: 'approve',
        dispatch,
      })

      router.push('/dashboard/customers')
    },
    ACCEPT_ACTIVATE_CUSTOMERS: async (request, dispatch, router) => {
      await activateCustomerProfileApprove({
        approvalId: request.id,
        comments: 'Approve customer profile activation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_CREATE_CUSTOMERS: async (request, dispatch, router, comments) => {
      await rejectCreateCustomer({
        approvalId: request.id,
        comments: comments || 'Reject customer creation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_LINK_ACCOUNTS: async (request, dispatch, router, comments) => {
      await approveRejectAccountLinking({
        approvalId: request.id,
        comments: comments || 'Approve customer account linking',
        profileId: request.entityId || '',
        dispatch,
        action: 'approve',
      })
    },
    //reject actions
    REJECT_UPDATE_CUSTOMERS: async (request, dispatch, router, comments) => {
      await rejectUpdateCustomer({
        approvalID: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_DEACTIVATE_CUSTOMERS: async (
      request,
      dispatch,
      router,
      comments
    ) => {
      await rejectDeactivateCustomer({
        approvalID: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_DELETE_CUSTOMERS: async (request, dispatch, router, comments) => {
      await approveDeleteCustomer({
        approvalID: request.id,
        comments: comments || '',
        type: 'reject',
        dispatch,
      })
      return router.push('/dashboard/customers')
    },
    REJECT_ACTIVATE_CUSTOMERS: async (request, dispatch, router, comments) => {
      await activateCustomerProfileReject({
        approvalId: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_LINK_ACCOUNTS: async (request, dispatch, router, comments) => {
      await approveRejectAccountLinking({
        approvalId: request.id,
        comments: comments || 'Reject customer account linking',
        profileId: request.entityId || '',
        dispatch,
        action: 'reject',
      })
    },
  },
  ProfileDevices: {
    //accept actions
    ACCEPT_DEACTIVATE_PROFILEDEVICES: async (request, dispatch, router) => {
      await acceptDeactivateCustomerDevice({
        approvalId: request.id,
        comments: 'Approve customer device deactivation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_ACTIVATE_PROFILEDEVICES: async (request, dispatch, router) => {
      await acceptActivateCustomerDevice({
        approvalId: request.id,
        comments: 'Approve customer device activation',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    //reject actions
    REJECT_DEACTIVATE_PROFILEDEVICES: async (
      request,
      dispatch,
      router,
      comments
    ) => {
      await rejectDeactivateCustomerDevice({
        approvalId: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_ACTIVATE_PROFILEDEVICES: async (
      request,
      dispatch,
      router,
      comments
    ) => {
      await rejectDeactivateCustomerDevice({
        approvalId: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
  },
  Profiles: {
    //accept actions
    ACCEPT_UPDATE_PROFILES: async (request, dispatch, _router) => {
      await approveCustomerPinReset({
        approvalID: request.id,
        comments: 'Approve customer pin reset',
        type: 'approve',
        dispatch,
      })
    },
    //reject actions
    REJECT_UPDATE_PROFILES: async (request, dispatch, router, comments) => {
      await approveCustomerPinReset({
        approvalID: request.id,
        comments: comments || '',
        type: 'reject',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
  },
  accounts: {
    //accept actions
    ACCEPT_CREATE_ACCOUNTS: async (request, dispatch, router, comments) => {
      await approveRejectAccountLinking({
        approvalId: request.id,
        comments: comments || 'Approve customer account linking',
        profileId: request.entityId || '',
        dispatch,
        action: 'approve',
      })
      dispatch(setCustomerApprovalBarOpen(false))
      router.push('/dashboard/customers')
    },
    ACCEPT_DEACTIVATE_ACCOUNTS: async (
      request,
      dispatch,
      _router,
      comments
    ) => {
      const profileId = JSON.parse(request.entity as string).profileId
      await acceptDeactivateCustomerAccount({
        approvalId: request.id,
        comments: comments || 'Deactivation approved',
        accountNo: request.entityId || '',
        profileId: profileId,
        dispatch,
      })
    },
    ACCEPT_ACTIVATE_ACCOUNTS: async (request, dispatch, _router) => {
      const profileId = JSON.parse(request.entity as string).profileId
      await acceptActivateCustomerAccount({
        approvalId: request.id,
        comments: 'Reject customer account deactivation',
        accountNo: request.entityId || '',
        profileId: profileId,
        dispatch,
      })
    },
    ACCEPT_RESTRICT_ACCOUNTS: async (request, dispatch, router) => {
      await acceptRestrictAccount({
        approvalId: request.id,
        comments: 'Approve customer account restriction',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    ACCEPT_DELETE_ACCOUNTS: async (request, dispatch, _router) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      await approveUnlinkCustomerAccount(dispatch, {
        approvalID: request.id,
        comments: 'Approve customer account unlink',
        profileID: profileId,
      })
      dispatch(setCustomerApprovalBarOpen(false))
    },
    //reject actions
    REJECT_DEACTIVATE_ACCOUNTS: async (request, dispatch, router, comments) => {
      const profileId = JSON.parse(request.entity as string).profileId

      await rejectDeactivateCustomerAccount({
        approvalId: request.id,
        comments: comments || '',
        accountNo: request.entityId || '',
        profileId: profileId,
        dispatch,
      })
    },
    REJECT_ACTIVATE_ACCOUNTS: async (request, dispatch, router, comments) => {
      const profileId = JSON.parse(request.entity as string).profileId

      await rejectActivateCustomerAccount({
        approvalId: request.id,
        comments: comments || 'Reject customer account activation',
        accountNo: request.entityId || '',
        profileId: profileId,
        dispatch,
      })
    },
    REJECT_RESTRICT_ACCOUNTS: async (request, dispatch, router, comments) => {
      await rejectRestrictAccount({
        approvalId: request.id,
        comments: comments || '',
        dispatch,
      })
      router.push('/dashboard/customers')
    },
    REJECT_DELETE_ACCOUNTS: async (request, dispatch, router, comments) => {
      const profileId = JSON.parse(request.entity as string).profileId
        ? JSON.parse(request.entity as string).profileId
        : request.entityId
      await rejectUnlinkCustomerAccount(dispatch, {
        approvalID: request.id,
        comments: comments || '',
        profileID: profileId,
      })
      dispatch(setCustomerApprovalBarOpen(false))
    },
    REJECT_CREATE_ACCOUNTS: async (request, dispatch, router, comments) => {
      await approveRejectAccountLinking({
        approvalId: request.id,
        comments: comments || 'Approve customer account linking',
        profileId: request.entityId || '',
        dispatch,
        action: 'reject',
      })
      dispatch(setCustomerApprovalBarOpen(false))
      router.push('/dashboard/customers')
    },
  },
  users: {
    //accept actions
    ACCEPT_CREATE_USERS: async (request, dispatch, _router, comments) => {
      await approveCreateUser(request.id, comments || '', dispatch)
    },
    ACCEPT_UPDATE_USERS: async (request, dispatch, _router, comments) => {
      await approveUpdateUser(
        request.entityId || '',
        { comments: comments || '' },
        dispatch
      )
    },
    ACCEPT_DEACTIVATE_USERS: async (request, dispatch, _router, comments) => {
      await deactivateUser(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
    },
    ACCEPT_ACTIVATE_USERS: async (request, dispatch, _router, comments) => {
      await activateUser(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
    },
    //reject actions
    REJECT_CREATE_USERS: async (request, dispatch, _router, comments) => {
      await rejectCreateUser(request.id, comments || '', dispatch)
    },
    REJECT_UPDATE_USERS: async (request, dispatch, _router, comments) => {
      await rejectUpdateUser(
        request.entityId || '',
        { comments: comments || '' },
        dispatch
      )
    },
    REJECT_DEACTIVATE_USERS: async (request, dispatch, _router, comments) => {
      await deactivateUser(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
    },
    REJECT_ACTIVATE_USERS: async (request, dispatch, _router, comments) => {
      await activateUser(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
    },
  },
  groups: {
    //accept actions
    ACCEPT_ACTIVATE_GROUPS: async (request, dispatch, _router, comments) => {
      await approveCreateRole(request.id, comments || '', dispatch)
    },
    ACCEPT_UPDATE_GROUPS: async (request, dispatch, _router, comments) => {
      await checkUpdateRole(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
    },
    ACCEPT_DELETE_GROUPS: async (request, dispatch, _router, comments) => {
      await checkDeleteRole(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
    },

    //reject actions
    REJECT_ACTIVATE_GROUPS: async (request, dispatch, _router, comments) => {
      await rejectCreateRole(request.id, comments || '', dispatch)
    },
    REJECT_UPDATE_GROUPS: async (request, dispatch, _router, comments) => {
      await checkUpdateRole(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
    },
    REJECT_DELETE_GROUPS: async (request, dispatch, _router, comments) => {
      await checkDeleteRole(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
    },
  },
}
