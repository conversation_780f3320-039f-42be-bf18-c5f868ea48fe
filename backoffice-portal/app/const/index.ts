export const CHIPCOLORS = {
  ACTIVE: {
    background: '#ECFDF3',
    color: '#12B76A',
  },
  APPROVED: {
    background: '#ECFDF3',
    color: '#12B76A',
  },
  DEACTIVATED: {
    background: '#F2F4F7',
    color: '#667085',
  },
  PENDING: {
    background: '#FFF6ED',
    color: '#FB6514',
  },
  NEW: {
    background: '#EFF8FF',
    color: '#175CD3',
  },
  REJECTED: {
    background: '#FEF3F2',
    color: '#F04438',
  },
  INACTIVE: {
    background: '#FEF3F2',
    color: '#F04438',
  },
  RESTRICTED: {
    background: '#FFF6ED',
    color: '#C4320A',
  },
}

export const ACCESS_CONTROLS = {
  //customers
  REJECT_APPROVALREQUEST_CUSTOMERS: [
    'REJECT_UPDATE_CUSTOMERS',
    'REJECT_ACTIVATE_CUSTOMERS',
    'REJECT_DEACTIVATE_CUSTOMERS',
    'REJECT_CREATE_CUSTOMERS',
  ],
  ACCEPT_APPROVALREQUEST_CUSTOMERS: [
    'ACCEPT_UPDATE_CUSTOMER',
    'ACCEPT_CREATE_CUSTOMERS',
    'ACCEPT_DEACTIVATE_CUSTOMER',
    'ACCEPT_ACTIVATE_CUSTOMERS',
  ],
  CREATE_CUSTOMERS: ['SUPER_CREATE_CUSTOMERS', 'MAKE_CREATE_CUSTOMERS'],
  UPDATE_CUSTOMERS: ['SUPER_UPDATE_CUSTOMERS', 'MAKE_UPDATE_CUSTOMERS'],
  DEACTIVATE_CUSTOMERS: [
    'MAKE_DEACTIVATE_CUSTOMERS',
    'SUPER_DEACTIVATE_CUSTOMERS',
  ],
  ACTIVATE_CUSTOMERS: ['MAKE_ACTIVATE_CUSTOMERS', 'SUPER_ACTIVATE_CUSTOMERS'],
  DELETE_CUSTOMERS: ['SUPER_DELETE_CUSTOMERS', 'MAKE_DELETE_CUSTOMERS'],
  VIEW_CUSTOMERS: ['VIEW_ALL_CUSTOMERS'],
  //profile devices
  CREATE_DEVICE: ['SUPER_CREATE_DEVICE', 'MAKE_CREATE_DEVICE'],
  ACTIVATE_DEVICE: ['SUPER_ACTIVATE_DEVICE', 'MAKE_ACTIVATE_DEVICE'],
  DEACTIVATE_DEVICE: ['SUPER_DEACTIVATE_DEVICE', 'MAKE_DEACTIVATE_DEVICE'],
  //users
  VIEW_USERS: ['VIEW_ALL_USERS'],
  CREATE_USERS: ['SUPER_CREATE_USERS', 'MAKE_CREATE_USERS'],
  UPDATE_USERS: ['SUPER_UPDATE_USERS', 'MAKE_UPDATE_USERS'],
  DEACTIVATE_ACTIVATE_USERS: [
    'MAKE_DEACTIVATE_USERS',
    'MAKE_ACTIVATE_USERS',
    'SUPER_DEACTIVATE_USERS',
    'SUPER_ACTIVATE_USERS',
  ],
  ACCEPT_APPROVALREQUEST_USERS: [
    'ACCEPT_DELETE_USERS',
    'ACCEPT_ACTIVATE_USERS',
    'ACCEPT_CREATE_USERS',
    'ACCEPT_UPDATE_USERS',
    'ACCEPT_DEACTIVATE_USERS',
  ],
  REJECT_APPROVALREQUEST_USERS: [
    'REJECT_CREATE_USERS',
    'REJECT_UPDATE_USERS',
    'REJECT_DEACTIVATE_USERS',
    'REJECT_ACTIVATE_USERS',
    'REJECT_DELETE_USERS',
  ],
  //roles
  CREATE_ROLES: ['MAKE_CREATE_GROUPS', 'SUPER_CREATE_GROUPS'],
  UPDATE_ROLES: ['MAKE_UPDATE_GROUPS', 'SUPER_UPDATE_GROUPS'],
  DELETE_ROLE: ['MAKE_DELETE_GROUPS', 'SUPER_DELETE_GROUPS'],
  REJECT_APPROVALREQUEST_ROLES: [
    'REJECT_ACTIVATE_GROUPS',
    'REJECT_UPDATE_GROUPS',
    'REJECT_DEACTIVATE_GROUPS',
    'REJECT_DELETE_GROUPS',
  ],
  ACCEPT_APPROVALREQUEST_ROLES: [
    'ACCEPT_ACTIVATE_GROUPS',
    'ACCEPT_UPDATE_GROUPS',
    'ACCEPT_DEACTIVATE_GROUPS',
    'ACCEPT_DELETE_GROUPS',
  ],
}

export const restrictReasons: string[] = [
  'Fraudulent Activity',
  'Suspicious Behavior',
  'Violation of terms',
  'Legal Requirement',
  'Other',
]
export const reasonsForUnlinking: string[] = [
  'User Request',
  'Security Concerns',
  'Account Inactivity',
  'Other',
]

export const reasonsForActivation: string[] = [
  'User Request',
  'Promotion Campaign',
  'System Error',
  'Other',
]

export const reasonsForDeleting: string[] = [
  'User Request',
  'Account Compromised',
  'Duplicate Account',
  'Violation of Terms of Service',
  'Other',
]
export const reasonsForDeactivating: string[] = [
  'User Request',
  'Temporary Suspension',
  'Fraudulent Activity',
  'Security Concerns',
  'Other',
]
