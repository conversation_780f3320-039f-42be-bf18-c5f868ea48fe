import React from 'react'
import { IconButton, IconButtonProps } from '@mui/material'

interface ICustomIconButtonProps extends IconButtonProps {
  icon: React.FC
  width?: number
  borderRadius?: string
  color?: IconButtonProps['color']
  border?: string
  backgroundColor?: string
  onClick?: () => void
}
export const CustomIconButton: React.FC<ICustomIconButtonProps> = ({
  icon: Icon,
  width = 40,
  borderRadius = '10px',
  border,
  color = 'primary',
  backgroundColor = 'transparent',
  onClick,
  ...props
}) => (
  <IconButton
    onClick={onClick}
    sx={{
      width,
      height: width,
      borderRadius,
      border,
      color,
      backgroundColor,
      '&:hover': {
        backgroundColor: color === 'primary' ? '#d3d3d3' : backgroundColor,
      },
    }}
    {...props}
  >
    {Icon && <Icon />}
  </IconButton>
)
