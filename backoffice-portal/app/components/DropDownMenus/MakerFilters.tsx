import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Grow,
  InputBase,
  List,
  ListItem,
  Paper,
  Popper,
  Typography,
} from '@mui/material'
import React from 'react'
import { sentenceCase } from 'tiny-case'

import { getUsers } from '@/app/redux/actions/staffUsers'
import { useAppDispatch, useAppSelector } from '@/app/redux'

import { CustomSkeleton } from '../Loading/CustomSkeleton'

interface MakerFiltersProps {
  variant?: 'contained' | 'outlined'
  onMakerFilterClick?: (makerId: string) => void
}

const MakerFilters: React.FC<MakerFiltersProps> = ({
  variant,
  onMakerFilterClick,
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = React.useState<boolean>(false)
  const [openChild, setOpenChild] = React.useState<boolean>(false)
  const [searchValue, setSearchValue] = React.useState<string>('')
  const [searchBy, setSearchBy] = React.useState<string>('firstName')
  const [selectedUser, setSelectedUser] = React.useState<string>('')
  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const anchorChildRef = React.useRef<HTMLButtonElement>(null)

  const { isLoadingUsers, usersResponse } = useAppSelector(
    (state) => state.users
  )

  const users = usersResponse?.data || []

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
    if (!open) {
      setSelectedUser('')
      setSearchValue('')
      getUsers(dispatch, { [searchBy]: searchValue, page: 0, size: 75 })
    }
  }
  return (
    <Box>
      <Button
        variant={variant || 'outlined'}
        sx={{
          height: '40px',
          minWidth: '107px',
          textWrap: 'nowrap',
          padding: '9px 28px',
          borderRadius: '6px',
          border: '1px solid  #AAADB0',
          background: variant === 'contained' ? '#000A12' : '#FFF',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          gap: 0,
        }}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        endIcon={<KeyboardArrowDownRounded />}
      >
        <Typography>
          Makers {selectedUser.length > 0 ? `: ${selectedUser}` : ''}
        </Typography>{' '}
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                padding: '10px',
                maxHeight: '40vh',
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
              }}
            >
              {/* search bar for users */}
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'row',
                  // padding: '10px'

                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
              >
                <Box>
                  <Button
                    variant="outlined"
                    sx={{
                      height: '40px',
                      minWidth: '100px',
                      textWrap: 'nowrap',
                      padding: ' 9px 10px 9px 20px',
                      borderRadius: '6px 0px 0px 6px',
                      borderRight: 'none',
                      borderLeft: '1px solid  #AAADB0',
                      borderTop: '1px solid  #AAADB0',
                      borderBottom: '1px solid  #AAADB0',
                      background: '#FFF',
                      justifyContent: 'flex-start',
                      gap: 0,
                    }}
                    endIcon={<KeyboardArrowDownRounded />}
                    onClick={() => setOpenChild(!openChild)}
                    ref={anchorChildRef}
                  >
                    <Typography>{sentenceCase(searchBy)}</Typography>
                  </Button>
                  <Popper
                    open={openChild}
                    anchorEl={anchorChildRef.current}
                    placement="bottom-start"
                  >
                    <Paper
                      sx={{
                        minWidth: '100px',
                        display: 'flex',
                        flexDirection: 'column',
                        padding: '10px',
                        border: '1px solid  #AAADB0',
                      }}
                    >
                      {['firstName', 'lastName', 'email'].map((item, index) => {
                        return (
                          <Button
                            sx={{
                              width: '120px',
                              height: '100%',
                              padding: '6px 6px 6px 15px',
                              justifyContent: 'flex-start',
                              color: 'black',
                            }}
                            key={index}
                            onClick={() => {
                              setSearchBy(item)
                              setOpenChild(false)
                            }}
                          >
                            <Typography>{sentenceCase(item)}</Typography>
                          </Button>
                        )
                      })}
                    </Paper>
                  </Popper>
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    padding: '10px',
                    border: '1px solid  #AAADB0',
                    height: '40px',
                    borderRadius: '0px 6px 6px 0px',
                  }}
                >
                  <InputBase
                    value={searchValue}
                    onChange={(e) => {
                      setSearchValue(e.target.value)
                      getUsers(dispatch, {
                        [searchBy]: e.target.value,
                        page: 0,
                        size: 75,
                      })
                    }}
                    placeholder={`Search by ${sentenceCase(searchBy)}`}
                  />
                </Box>
              </Box>

              <Box
                sx={{
                  width: '100%',
                  height: '30vh',
                  overflowY: 'scroll',
                }}
              >
                <List>
                  {!isLoadingUsers &&
                    users.map((user, index) => {
                      return (
                        <ListItem
                          key={index}
                          sx={{
                            padding: '4px',
                          }}
                        >
                          <Button
                            sx={{
                              width: '100%',
                              height: '100%',
                              border: '1px solid #AAADB0',
                              borderRadius: '6px',
                              padding: '6px 6px 6px 15px',
                              justifyContent: 'flex-start',
                            }}
                            onClick={() => {
                              const makerID = user.id
                              onMakerFilterClick && onMakerFilterClick(makerID)
                              setSelectedUser(
                                `${user.firstName} ${user.lastName}`
                              )

                              setOpen(false)
                            }}
                          >
                            <Typography>
                              {user.firstName} {user.lastName}
                            </Typography>
                          </Button>
                        </ListItem>
                      )
                    })}

                  {isLoadingUsers &&
                    Array.from({ length: 10 }).map((_, index) => {
                      return (
                        <ListItem
                          key={index}
                          sx={{
                            padding: '4px',
                          }}
                        >
                          <CustomSkeleton
                            variant="rectangular"
                            sx={{
                              width: '100%',
                              height: '40px',
                              borderRadius: '6px',
                            }}
                          />
                        </ListItem>
                      )
                    })}
                </List>
              </Box>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Box>
  )
}

export default MakerFilters
