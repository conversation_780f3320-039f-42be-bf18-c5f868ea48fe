import React from 'react'
import PropTypes from 'prop-types'
import Tabs from '@mui/material/Tabs'
import Tab, { TabOwnProps } from '@mui/material/Tab'
import { styled } from '@mui/material/styles'

export function TabPanel(props: {
  [x: string]: unknown
  children: React.ReactNode
  value: number
  index: number
}) {
  const { children, value, index, ...other } = props
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`scrollable-auto-tabpanel-${index}`}
      aria-labelledby={`scrollable-auto-tab-${index}`}
      {...other}
    >
      {value === index && <div>{children}</div>}
    </div>
  )
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
}

export const AntTabs = styled(Tabs)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  marginRight: theme.spacing(3),
  marginBottom: theme.spacing(0),
  // borderBottom: '1px solid',
  borderBottomColor: theme.palette.primary.main,
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.main,
  },
}))

export const AntTab = styled((props: TabOwnProps) => (
  <Tab disableRipple {...props} />
))(({ theme }) => ({
  textTransform: 'none',
  minWidth: 0,
  [theme.breakpoints.up('sm')]: {
    minWidth: 0,
  },
  fontWeight: '500',
  color: '#667085',
  fontSize: '14px',
  lineHeight: '20px',
  marginRight: theme.spacing(1),
  '&:hover': {
    color: '#667085',
    opacity: 1,
  },
  '&.Mui-selected': {
    color: '#2A3339',
    fontWeight: theme.typography.fontWeightBold,
    borderRadius: '5px',
  },
  '&.Mui-focusVisible': {
    backgroundColor: '#d1eaff',
  },
}))

export const SecondaryTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  border: '1px solid var(--gray-300, #D0D5DD)',
  borderRadius: '4px',
  '& .MuiTabs-indicator': {
    backgroundColor: 'transparent',
  },
}))

export const SecondaryTab = styled((props: { label: string; key: string }) => (
  <Tab disableRipple {...props} />
))(({ theme }) => ({
  textTransform: 'none',
  minWidth: 0,
  [theme.breakpoints.up('sm')]: {
    minWidth: 0,
  },
  borderRight: '1px solid #D0D5DD',
  padding: '10px 36px',
  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
  fontWeight: '500',
  fontSize: '16px',
  '&:hover': {
    color: '#667085',
    opacity: 1,
  },
  '&.Mui-selected': {
    color: '#344054',
    background: '#D0D5DD',
  },
  '&.Mui-focusVisible': {
    backgroundColor: '#d1eaff',
  },
}))
