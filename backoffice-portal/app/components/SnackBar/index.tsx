import React, { useEffect, useState } from 'react'
import Snackbar from '@mui/material/Snackbar'
import Mu<PERSON><PERSON><PERSON>t, { AlertProps } from '@mui/material/Alert'
import { styled } from '@mui/material/styles'

import { useAppDispatch, useAppSelector } from '@/app/redux'
import { clearNotification } from '@/app/redux/reducers/notifications'

// eslint-disable-next-line react/display-name
const Alert = React.forwardRef<HTMLDivElement, AlertProps>((props, ref) => (
  <MuiAlert elevation={6} ref={ref} variant="outlined" {...props} />
))

const CustomSnackbar = styled(Snackbar)(({ theme }) => ({
  marginTop: theme.spacing(15),
  [theme.breakpoints.up('md')]: {
    marginBottom: theme.spacing(5),
  },
}))

interface LocalNotificationProps {
  clearMessage?: () => void
}

const LocalNotification: React.FC<LocalNotificationProps> = () => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'

  // Consider extracting the duration to a constants file if used across components
  const DEFAULT_DURATION = 10000
  const ERROR_DURATION = 6000

  useEffect(() => {
    if (notification) {
      setOpen(true)
    }
  }, [notification, notificationType])

  const handleClose = (_event?: unknown, reason?: string) => {
    if (reason === 'clickaway') {
      return
    }
    dispatch(clearNotification())
    setOpen(false)
  }

  if (!notification) return null

  return (
    <CustomSnackbar
      open={open}
      autoHideDuration={
        notificationType === 'error' ? ERROR_DURATION : DEFAULT_DURATION
      }
      onClose={handleClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
    >
      <Alert
        onClose={handleClose}
        severity={notificationType}
        sx={{
          border: 'none',
          borderLeft: '5px solid',
          borderColor: `${
            notificationType === 'success'
              ? '#12B76A'
              : notificationType === 'warning'
                ? '#E16012'
                : '#D92D20'
          }`,
          background: '#FFFFFF',
          px: '10px',
          py: '10px',
        }}
      >
        {notification}
      </Alert>
    </CustomSnackbar>
  )
}

export default LocalNotification
