import React, { useMemo } from 'react'
import { styled } from '@mui/system'
import { Tabs } from '@mui/base/Tabs'
import { TabsList as BaseTabsList } from '@mui/base/TabsList'
import { TabPanel as BaseTabPanel } from '@mui/base/TabPanel'
import { buttonClasses } from '@mui/base/Button'
import { Tab as BaseTab, tabClasses } from '@mui/base/Tab'
import { SearchRounded } from '@mui/icons-material'
import { Paper, Stack } from '@mui/material'

import { ITableData } from '@/app/interfaces/shared'
import { CustomSearchInput } from '@/app/components/Input/CustomSearchInput'

interface CustomToggleProps {
  data: ITableData[]
  renderTable: (filteredData: ITableData[]) => JSX.Element
  onChange?: (value: number) => void
}

const CustomToggle: React.FC<CustomToggleProps> = ({
  data,
  renderTable,
  onChange,
}) => {
  const [search, setSearch] = React.useState<string>('')
  const [selectedTab, setSelectedTab] = React.useState<number>(0)

  const handleSearch = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setSearch(event.target.value)
  }

  type TableDataKey = 'event' | 'eventDate'

  const filteredData = useMemo(() => {
    const filtered = data.filter((d) =>
      (['event', 'eventDate'] as TableDataKey[]).some((key) =>
        // (['event', 'eventSource', 'eventDate'] as TableDataKey[]).some((key) =>
        d[key].toString().toLowerCase().includes(search.toLowerCase())
      )
    )

    if (selectedTab === 0) {
      return filtered.filter((d) => d)
    } else {
      return filtered.filter((d) => d)
    }
  }, [search, data, selectedTab])

  const renderTablePaper = () => (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid #EAECF0',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      {renderTable(filteredData)}
    </Paper>
  )

  return (
    <Tabs
      value={selectedTab}
      onChange={(
        _event: React.SyntheticEvent<Element, Event> | null,
        newValue: number | string | null
      ) => {
        if (newValue !== null) {
          onChange && onChange(Number(newValue))
          setSelectedTab(Number(newValue))
        }
      }}
    >
      <Stack
        direction="row"
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
          width: '100%',
        }}
      >
        <CustomSearchInput
          sx={{
            width: '62%',
            '&.Mui-focused': {
              width: '62%',
            },
          }}
          startAdornment={<SearchRounded />}
          placeholder="Search Event"
          onChange={handleSearch}
          value={search}
        />
        <TabsList>
          <Tab value={0}>From Back Office</Tab>
          <Tab value={1}>From Customer</Tab>
        </TabsList>
      </Stack>
      <TabPanel value={0}>{renderTablePaper()}</TabPanel>
      <TabPanel value={1}>{renderTablePaper()}</TabPanel>
    </Tabs>
  )
}

const grey = {
  50: '#F3F6F9',
  100: '#E5EAF2',
  200: '#DAE2ED',
  300: '#C7D0DD',
  400: '#B0B8C4',
  500: '#9DA8B7',
  600: '#6B7A90',
  700: '#434D5B',
  800: '#303740',
  900: '#1C2025',
  950: '#D0D5DD',
  951: '#EAECF0',
}

export const Tab = styled(BaseTab)`
  color: #000;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: transparent;
  width: 50%;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 7px;
  display: flex;
  justify-content: center;
  align-items: center;

  &:hover {
    background-color: ${grey[100]};
    color: #000;
  }

  &:focus {
    color: #fff;
    outline: 1.5px solid ${grey[950]};
  }

  &.${tabClasses.selected} {
    background-color: #fff;
    color: #000;
  }

  &.${buttonClasses.disabled} {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

export const TabPanel = styled(BaseTabPanel)(
  ({ theme }) => `
  width: 100%;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  background: ${grey[900]};
  border: 1px solid ${theme.palette.mode === 'dark' ? grey[700] : grey[200]};
  border-radius: 12px;
  opacity: 0.6;
  `
)

export const TabsList = styled(BaseTabsList)(
  () => `
  width: 28%;
  background-color: ${grey[951]};
  border: 2px solid ${grey[950]};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-content: space-between;
  `
)

export default CustomToggle
