import React from 'react'
import Button from '@mui/material/Button'

import {
  ExportIcon,
  FileDownloadIcon,
} from '@/app/components/SvgIcons/ExportIcon'

interface ExportButtonProps {
  onClick: () => void
  text: string
  openFilter: boolean
}

const ExportButton: React.FC<ExportButtonProps> = ({
  onClick,
  text,
  openFilter,
}) => {
  return (
    <Button
      onClick={onClick}
      type="submit"
      startIcon={openFilter ? <FileDownloadIcon /> : <ExportIcon />}
      size="medium"
      variant="contained"
      sx={{
        backgroundColor: '#E7E8E9',
        color: '#555C61',
        paddingX: '44px',
        paddingY: '8px',
        fontSize: '1rem',
        fontWeight: 'medium',
        textTransform: 'none',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textWrap: 'noWrap',
        minWidth: '120px',
        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
        '&:hover': {
          backgroundColor: '#E7E8E9',
        },
      }}
    >
      {text}
    </Button>
  )
}

export default ExportButton
