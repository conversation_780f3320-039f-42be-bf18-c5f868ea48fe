import { OutlinedInput, styled } from '@mui/material'

interface CustomSearchInputProps {
  width?: string
}

export const CustomSearchInput = styled(OutlinedInput)<CustomSearchInputProps>(
  ({ theme, width = '400px' }) => ({
    width: width,
    height: 40,
    transition: theme.transitions.create(['box-shadow', 'width'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.shorter,
    }),
    boxShadow: '0px 1px 2px 0px #1018280D',
    '&.Mui-focused': {
      boxShadow: '0px 0px 0px 4px #D3CFDC3D',
    },
    '& fieldset': {
      borderRadius: '4px',
      border: '1px solid #D0D5DD !important',
    },
  })
)
