import { FormControlLabel, FormControlLabelProps, styled } from '@mui/material'

export const CustomFormControlLabel = styled((props: FormControlLabelProps) => (
  <FormControlLabel {...props} />
))(({ theme }) => ({
  '&.MuiFormControlLabel-root': {
    justifyContent: 'space-between',
    borderRadius: '4px',
    border: '1px solid #E3E4E4',
    margin: 0,
  },
  '.MuiFormControlLabel-label': {
    color: theme.palette.primary.main,
    fontWeight: 400,
    marginLeft: '10px',
    fontSize: '15px',
  },
}))
