'use client'
import { Autocomplete, styled, TextField } from '@mui/material'
import React from 'react'

export interface IEmail {
  email: string
  firstName: string
  lastName: string
}

// TODO: DELETE
export const emails: IEmail[] = [
  {
    firstName: 'Caldewood',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: 'Aliku<PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: 'Aliku<PERSON>',
    email: '<EMAIL>',
  },
  {
    firstName: 'Caldewood',
    lastName: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
  },
]
const ListItem = styled('li')(
  () => `


`
)
interface IAutoComplete<T, K extends keyof T> {
  label: string
  options: T[]
  value?: T | null
  optionLabel: K
  fullWidth?: boolean
  isOptionEqualToValue?: (option: T, value: T | null) => boolean
  onChange: (
    event: React.SyntheticEvent<Element, Event>,
    value: T | null
  ) => void
  renderListItem: (option: T) => React.ReactNode
}

const AutoComplete = <T, K extends keyof T>({
  label,
  options,
  value,
  optionLabel,
  fullWidth,
  onChange,
  renderListItem,
  isOptionEqualToValue,
}: IAutoComplete<T, K>) => {
  type OptionType = T

  return (
    <Autocomplete
      fullWidth={fullWidth}
      onChange={onChange}
      value={value}
      sx={{
        width: fullWidth ? '100%' : '',
      }}
      getOptionLabel={(option) => String(option[optionLabel])}
      isOptionEqualToValue={isOptionEqualToValue}
      renderOption={(params, option: OptionType) => (
        <ListItem {...params} sx={{ width: '100%' }}>
          {renderListItem(option)}
        </ListItem>
      )}
      renderInput={(params) => {
        return (
          <TextField
            {...params}
            inputProps={{ ...params.inputProps }}
            label={label}
          />
        )
      }}
      options={options}
    />
  )
}

export default AutoComplete
