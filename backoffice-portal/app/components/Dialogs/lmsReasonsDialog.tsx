import React, { useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  IconButton,
  TextField,
  Typography,
} from '@mui/material'
import { ChevronRight, Close } from '@mui/icons-material'

import { LoadingButton } from '../Loading/LoadingButton'

interface reasonProps {
  open: boolean
  title: string
  buttonText: string
  isLoading: boolean
  setOpen:
    | React.Dispatch<React.SetStateAction<boolean>>
    | ((val: boolean) => void)
  onClick?: (reasons: string) => void
  buttonProps?: {
    color: string
  }
}

export const ReasonsDialog: React.FC<reasonProps> = ({
  open,
  title,
  buttonText,
  isLoading,
  onClick,
  setOpen,
  buttonProps,
}) => {
  const [reason, setReason] = useState<string>('')
  const [hasTouched, setHasTouched] = useState(false)
  const [error, setError] = useState<string>('')

  const handleKeyDown = (event: React.KeyboardEvent) => {
    event.stopPropagation() // Stop the event from propagating up to the parent elements
  }
  const handleClose = () => {
    setOpen(false)
  }
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const input = event.target.value
    setReason(input)

    setHasTouched(true)
    if (!validateReason(input)) {
      setError(
        'Reason must be at least 10 characters and contain only valid characters.'
      )
    } else {
      setError('')
    }
  }
  const validateReason = (reason: string) => {
    const trimmedReason = reason.trim()
    return (
      trimmedReason.length >= 10 && /^[a-zA-Z0-9\s.,!?'-]*$/.test(trimmedReason)
    )
  }

  const isActionDisabled = reason.trim().length < 10 || !validateReason(reason)
  return (
    <Dialog open={open} onClose={handleClose} maxWidth={'sm'} fullWidth>
      <Box
        sx={{
          bgcolor: 'background.paper',
          border: '0.1vw solid #ccc',
          borderRadius: '0.3vw',
          boxShadow: 1,
          maxWidth: '100%',
          overflowY: {
            xs: 'auto',
            md: 'hidden',
          },
          padding: '1vw',
        }}
      >
        <Box
          sx={{
            height: '2vw',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontWeight: 'bold', color: '#333', fontSize: '1vw' }}
          >
            {title}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              border: '0.8px solid #CBD5E1',
              borderRadius: '50%',
              padding: '0.2vw',
              '&:hover': {
                backgroundColor: '#E2E8F0',
              },
            }}
          >
            <Close fontSize="small" />
          </IconButton>
        </Box>

        <TextField
          sx={{
            marginTop: '1vw',
            marginBottom: '1vw',
          }}
          variant="filled"
          fullWidth
          onKeyDown={handleKeyDown}
          multiline
          rows={4}
          placeholder="Leave a comment:"
          value={reason}
          onChange={handleChange}
          error={Boolean(hasTouched && error)}
          helperText={hasTouched && error ? error : ''}
        />
        <Typography variant="label1">
          Once you click save changes, your updates will be submitted to your
          manager for verification.
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            paddingTop: '1vw',
          }}
        >
          <Button
            variant="outlined"
            onClick={handleClose}
            sx={{
              color: '#ffffff',
              marginRight: '1vw',
              minWidth: '10vw',
              background: (buttonProps && buttonProps.color) || '#EB0045',
              border: `0.1vw solid ${(buttonProps && buttonProps.color) || '#EB0045'}`,
              '&:hover': {
                background: (buttonProps && buttonProps.color) || '#EB0045 ',
                opacity: '0.8',
                color: '#ffffff',
              },
            }}
          >
            Cancel
          </Button>
          {isLoading ? (
            <LoadingButton />
          ) : (
            <Button
              variant="contained"
              disabled={isActionDisabled}
              onClick={() => {
                onClick && onClick(reason)
                setOpen(false)
              }}
              sx={{
                minWidth: '10vw',
              }}
              endIcon={<ChevronRight />}
            >
              {buttonText}
            </Button>
          )}
        </Box>
      </Box>
    </Dialog>
  )
}
