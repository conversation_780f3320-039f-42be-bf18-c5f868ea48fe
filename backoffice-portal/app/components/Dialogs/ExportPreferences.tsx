import React, { useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  RadioGroup as MuiRadioGroup,
  Radio,
  Stack,
  Typography,
} from '@mui/material'
import { Close } from '@mui/icons-material'

import { ExportFinalIcon } from '@/app/components/SvgIcons/ExportIcon'
import { FileFormat } from '@/app/redux/actions/staffUsers'

interface CheckboxOption {
  label: string
  value: FileFormat
  disabled?: boolean
}

interface ExportPreferencesProps {
  open: boolean
  onExport: (format: FileFormat) => void
  onCancel: () => void
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  selectedIds: string[]
}

const ExportPreferences: React.FC<ExportPreferencesProps> = ({
  open,
  onExport,
  onCancel,
  setOpen,
}) => {
  const [selectedFormat, setSelectedFormat] = useState<FileFormat>('excel')

  const formatOptions: CheckboxOption[] = [
    { label: 'Excel', value: 'excel', disabled: false },
    { label: 'JSON', value: 'json', disabled: true },
    { label: 'PDF', value: 'pdf', disabled: true },
    { label: 'CSV', value: 'csv', disabled: true },
  ]

  const handleClose = () => {
    setOpen(false)
  }

  const handleFormatChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFormat(event.target.value as FileFormat)
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth={'xs'} fullWidth>
      <Box
        sx={{
          bgcolor: 'background.paper',
          border: '1px solid #ccc',
          borderRadius: '4px',
          boxShadow: 1,
          overflowY: {
            xs: 'auto',
            md: 'hidden',
          },
        }}
      >
        <Box
          sx={{
            bgcolor: '#F2F4F7',
            p: 2,
            borderBottom: '2px solid #e0e0e0',
            height: '42px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Typography
            variant="h6"
            sx={{ fontWeight: 'bold', color: '#333', fontSize: '16px' }}
          >
            Export preferences
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              border: '0.8px solid #CBD5E1',
              borderRadius: '50%',
              padding: '4px',
              '&:hover': {
                backgroundColor: '#E2E8F0',
              },
            }}
          >
            <Close fontSize="small" />
          </IconButton>
        </Box>

        <Box sx={{ p: 2 }}>
          <Stack direction="column" spacing={2}>
            <FormControl component="fieldset">
              <FormLabel
                component="legend"
                sx={{ fontSize: '1rem', color: '#2A3339', mb: 1 }}
              >
                Data Format
              </FormLabel>
              <MuiRadioGroup
                value={selectedFormat}
                onChange={handleFormatChange}
              >
                {formatOptions.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    value={option.value}
                    control={<Radio sx={{ color: '#D0D5DD' }} />}
                    label={option.label}
                    disabled={option.disabled}
                  />
                ))}
              </MuiRadioGroup>
            </FormControl>
          </Stack>
          <Divider />
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 2 }}>
          <Button
            variant="outlined"
            onClick={onCancel}
            sx={{
              borderColor: '#D0D5DD',
              '&:hover': {
                borderColor: '#D0D5DD',
                backgroundColor: 'rgba(208, 213, 221, 0.1)',
              },
            }}
            fullWidth
          >
            Cancel
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              onExport(selectedFormat)
            }}
            sx={{ ml: 1 }}
            endIcon={<ExportFinalIcon />}
            disabled={!selectedFormat}
            fullWidth
          >
            Export
          </Button>
        </Box>
      </Box>
    </Dialog>
  )
}

export default ExportPreferences
