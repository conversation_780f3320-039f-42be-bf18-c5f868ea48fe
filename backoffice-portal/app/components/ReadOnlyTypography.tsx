import { TextField, TextFieldProps } from '@mui/material'

export const ReadOnlyTypography = (props: TextFieldProps) => {
  return (
    <TextField
      fullWidth
      size="small"
      InputProps={{
        readOnly: true,
      }}
      InputLabelProps={{
        shrink: true,
      }}
      sx={{
        '.MuiOutlinedInput-notchedOutline': {
          borderColor: '#ACACAC !important',
        },
        '.MuiInputLabel-root': {
          color: 'text.primary',
        },
      }}
      multiline
      {...props}
    />
  )
}
