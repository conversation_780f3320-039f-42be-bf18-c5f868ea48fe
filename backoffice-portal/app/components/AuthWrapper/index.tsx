'use client'
import { FC, ReactNode, useEffect } from 'react'

import { useCustomRouter } from '@/app/utils/helpers'
import { LoadingFullScreen } from '@/app/components/Loading'
import { isLoggedIn } from '@/app/utils/authHelper'

export interface AuthWrapperProps {
  children: ReactNode
  requiresAuth?: boolean
}
const AuthWrapper: FC<AuthWrapperProps> = ({
  children,
  requiresAuth = true,
}) => {
  const router = useCustomRouter()
  const isAuthenticated = isLoggedIn()

  useEffect(() => {
    if (requiresAuth && !isAuthenticated) {
      router.push('/')
    } else if (!requiresAuth && isAuthenticated) {
      router.push('/dashboard/home ')
    }
  }, [isAuthenticated, requiresAuth, router])

  if (
    (requiresAuth && !isAuthenticated) ||
    (!requiresAuth && isAuthenticated)
  ) {
    return (
      <>
        <LoadingFullScreen />
      </>
    )
  }

  return <>{children}</>
}

export default AuthWrapper
