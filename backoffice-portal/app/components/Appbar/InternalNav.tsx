'use client'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>con<PERSON>utton,
  <PERSON>u,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@mui/material'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import AccountCircleIcon from '@mui/icons-material/AccountCircle'

import { useCustomRouter } from '@/app/utils/helpers'
import { handleLogout, refreshToken } from '@/app/redux/actions/auth'
import { useAppDispatch, useAppSelector } from '@/app/redux'

const topNavItems = [
  {
    label: 'Settings',
    icon: '/icons/settings.svg',
  },
  {
    label: 'FAQs',
    icon: '/icons/help-circle.svg',
  },
  {
    label: 'Notifications',
    icon: '/icons/bell.svg',
  },
]

const InternalNavBar = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget)
  }

  const handleCloseUserMenu = () => {
    setAnchorElUser(null)
  }
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)
  useEffect(() => {
    const refresh = setInterval(refreshToken, 1000 * 60 * 20)
    return () => clearInterval(refresh)
  }, [])
  return (
    <AppBar
      position="static"
      sx={{
        boxShadow: 'none',
        height: '70px',
        backgroundColor: '#FFFFFF',
        px: '2%',
        borderBottom: '1px solid var(--Color-Stroke-Stroke-2, #E3E4E4)',
      }}
    >
      <Toolbar
        disableGutters
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-end',
          border: 'none',
          boxShadow: 'none',
          width: '100%',
        }}
      >
        {topNavItems.map((item, index) => (
          <IconButton key={index}>
            <Image src={item.icon} alt={item.label} width={24} height={24} />
          </IconButton>
        ))}
        <Tooltip title="Open profile">
          <IconButton
            onClick={handleOpenUserMenu}
            sx={{
              p: 0,
              borderRadius: '16px',
            }}
          >
            <Chip
              icon={
                <AccountCircleIcon
                  sx={{
                    color: '#000A12',
                    fontSize: 32,
                    marginLeft: '0px !important',
                  }}
                />
              }
              label={`${(profile && profile.first_name) || ''} ${
                (profile && profile.last_name) || ''
              }`}
              sx={{
                fontWeight: '500',
                fontSize: '14px',
                backgroundColor: '#EAECF0',
                gap: '8px',
              }}
            />
          </IconButton>
        </Tooltip>
        <Menu
          sx={{
            mt: '45px',
            display: 'flex',
            flexDirection: 'column',
          }}
          id="menu-appbar"
          anchorEl={anchorElUser}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          keepMounted
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          open={Boolean(anchorElUser)}
          onClose={handleCloseUserMenu}
        >
          <MenuItem onClick={() => handleLogout(dispatch, router)}>
            Logout
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  )
}

export default InternalNavBar
