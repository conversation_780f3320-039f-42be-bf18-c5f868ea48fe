'use client'
import {
  AppBar,
  Box,
  Chip,
  IconButton,
  Link,
  Menu,
  MenuItem,
  Paper,
  Toolbar,
  Tooltip,
} from '@mui/material'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import AccountCircleIcon from '@mui/icons-material/AccountCircle'

import { useCustomRouter } from '@/app/utils/helpers'
import { handleLogout, refreshToken } from '@/app/redux/actions/auth'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { LogoPlain } from '@/app/components/SvgIcons/LogoPlain'

const topNavItems = [
  {
    label: 'Settings',
    icon: '/icons/settings.svg',
  },
  {
    label: 'FAQs',
    icon: '/icons/help-circle.svg',
  },
  {
    label: 'Notifications',
    icon: '/icons/bell.svg',
  },
]

const Index = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget)
  }

  const handleCloseUserMenu = () => {
    setAnchorElUser(null)
  }
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)
  useEffect(() => {
    const refresh = setInterval(refreshToken, 1000 * 60 * 3)
    return () => clearInterval(refresh)
  }, [])
  return (
    <AppBar
      position="static"
      sx={{
        boxShadow: 'none',
        backgroundColor: '#FCFCFC',
        borderBottom: '1px solid #E3E4E4',
      }}
    >
      <Paper
        elevation={0}
        sx={{
          paddingLeft: '1%',
          paddingRight: '2%',
          borderBottom: '1px solid #E3E4E4',
        }}
      >
        <Toolbar
          disableGutters
          sx={{
            display: 'flex',
            flexDirection: 'column',
            border: 'none',
            boxShadow: 'none',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              gap: '10px',
            }}
          >
            <Box
              sx={{
                width: '243px',
                height: '100%',
                display: 'flex',
              }}
            >
              <Link href={'/landing/'}>
                <LogoPlain />
              </Link>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-end',
                gap: '10px',
                width: '50%',
                height: '100%',
                alignItems: 'center',
                marginTop: '10px',
              }}
            >
              {topNavItems.map((item, index) => (
                <IconButton key={index}>
                  <Image
                    src={item.icon}
                    alt={item.label}
                    width={24}
                    height={24}
                  />
                </IconButton>
              ))}
              <Tooltip title="Open profile">
                <IconButton
                  onClick={handleOpenUserMenu}
                  sx={{
                    p: 0,
                    borderRadius: '16px',
                  }}
                >
                  <Chip
                    icon={
                      <AccountCircleIcon
                        sx={{
                          color: '#000A12',
                          fontSize: 32,
                          marginLeft: '0px !important',
                        }}
                      />
                    }
                    label={`${(profile && profile.first_name) || ''} ${
                      (profile && profile.last_name) || ''
                    }`}
                    sx={{
                      fontWeight: '500',
                      fontSize: '14px',
                      backgroundColor: '#EAECF0',
                      gap: '8px',
                    }}
                  />
                </IconButton>
              </Tooltip>
              <Menu
                sx={{
                  mt: '45px',
                  display: 'flex',
                  flexDirection: 'column',
                }}
                id="menu-appbar"
                anchorEl={anchorElUser}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
              >
                <MenuItem onClick={() => handleLogout(dispatch, router)}>
                  Logout
                </MenuItem>
              </Menu>
            </Box>
          </Box>
        </Toolbar>
      </Paper>
    </AppBar>
  )
}

export default Index
