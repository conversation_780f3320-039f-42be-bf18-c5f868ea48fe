'use client'
import {
  Box,
  IconButton,
  Link,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  styled,
  Tooltip,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

import { HasAccessToRights } from '@/app/utils/AccessControlHelper'
import { useCustomRouter } from '@/app/utils/helpers'
import { sidebarConfig } from '@/app/components/Sidebar/SidebarConfig'
import { ISidebarConfigItem } from '@/app/interfaces/shared'
import {
  InternalCollapsedLogo,
  InternalNavLogo,
} from '@/app/components/SvgIcons/LogoPlain'
import { NavMenuIcon } from '@/app/components/SvgIcons/SidebarIcons'
import { useAppDispatch } from '@/app/redux'
import { setSidebarCollapsed } from '@/app/redux/reducers/navigation'

const ListItemStyle = styled(ListItemButton)(() => ({
  height: 48,
  position: 'relative',
  display: 'flex',
  flexDirection: 'row',
  flexWrap: 'nowrap',
  textTransform: 'capitalize',
  color: '#2A3339',
  '&:hover': {
    backgroundColor: '#F3F4F5',
    color: '#2A3339',
  },
}))
const NavBarStyle = styled(Box)(() => ({
  backgroundColor: '#FAFAFA',
  paddingTop: '1.5%',
  minHeight: `100vh`,
  borderRight: '1px solid #E7E8E9',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  gap: '10px',
}))

const ListItemIconStyle = styled(ListItemIcon)({
  width: 22,
  height: 22,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
})
type MatchPathOptions = {
  path: string
  end: boolean
}

const matchPath = (options: MatchPathOptions, pathname: string) => {
  const { path, end } = options
  const regex = new RegExp(`^${path}${end ? '$' : ''}`, 'i')
  return regex.test(pathname)
}

function NavItem({
  item,
  active,
  collapsed,
  key,
}: {
  item: ISidebarConfigItem
  active: (path: string) => boolean
  collapsed: boolean
  key: string
}) {
  const { title, path, icon } = item
  const isActiveRoot = active(path)

  const activeRootStyle = {
    color: '#000A12',
    fontWeight: 'fontWeightMedium',
    backgroundColor: '#E7E8E9',
    '&:before': { display: 'flex' },
    '& $title': {
      fontWeight: 400,
    },
    '& $icon': {
      color: '#000A12',
    },
    borderRadius: '4px',
  }
  const router = useCustomRouter()
  return (
    <Link key={key} onClick={() => router.push(path)} underline={'none'}>
      <ListItemStyle
        disableGutters
        sx={{
          ...(isActiveRoot && activeRootStyle),
        }}
      >
        <Tooltip title={collapsed ? title : ''} placement={'right'}>
          <ListItemIconStyle>{icon && icon}</ListItemIconStyle>
        </Tooltip>
        {!collapsed && (
          <ListItemText
            disableTypography
            primary={title}
            sx={{
              minWidth: '150px',
            }}
          />
        )}
      </ListItemStyle>
    </Link>
  )
}
const Sidebar = ({ app }: { app: string }) => {
  const pathname = usePathname()
  const dispatch = useAppDispatch()
  const isProductionEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === 'production'
  const handleCollapse = () => {
    setCollapsed(!collapsed)
    dispatch(setSidebarCollapsed(!collapsed))
  }
  const [collapsed, setCollapsed] = useState(false)
  const match = (path: string) =>
    path ? matchPath({ path, end: false }, pathname) : false
  const handleResize = () => {
    const shouldCollapse = window.innerWidth < 1300
    setCollapsed(shouldCollapse)
    dispatch(setSidebarCollapsed(shouldCollapse))
  }
  useEffect(() => {
    window.addEventListener('resize', handleResize)
    handleResize()
    return () => window.removeEventListener('resize', handleResize)
  }, [window.innerWidth])
  return (
    <NavBarStyle
      sx={{
        minWidth: collapsed ? '5vw' : '12vw',
        px: collapsed ? '1%' : '2%',
        transition: 'min-width 0.3s, padding 0.3s',
      }}
    >
      <IconButton
        href={'/landing/'}
        sx={{
          '&:hover': {
            backgroundColor: 'transparent',
          },
          padding: '0',
          paddingLeft: '3.5%',
          transition: 'all 0.3s',
        }}
      >
        {' '}
        <Box
          sx={{
            width: collapsed ? '44px' : '123px',
            transition: 'width 0.3s',
          }}
        >
          {collapsed ? <InternalCollapsedLogo /> : <InternalNavLogo />}
        </Box>
      </IconButton>
      <Tooltip
        title={collapsed ? 'Expand Menu' : 'Collapse Menu'}
        placement={'right'}
      >
        <IconButton
          onClick={handleCollapse}
          sx={{
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          <NavMenuIcon />
        </IconButton>
      </Tooltip>

      <List disablePadding>
        {sidebarConfig.map((item) => {
          if (
            item.app === app &&
            ((item.isProductionReady === true && isProductionEnv) ||
              !isProductionEnv) &&
            (item.requiredRights.length < 1 ||
              HasAccessToRights([...item.requiredRights]))
          )
            return (
              <NavItem
                key={item.title}
                item={item}
                active={match}
                collapsed={collapsed}
              />
            )
        })}
      </List>
    </NavBarStyle>
  )
}

export default Sidebar
