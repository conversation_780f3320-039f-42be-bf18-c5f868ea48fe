'use client'

import ShieldOutlinedIcon from '@mui/icons-material/ShieldOutlined'

import { CustomProductsIcon } from '@/app/components/SvgIcons/ProductsIcon'
import {
  AdvertisingIcon,
  AuditTrailIcon,
  BeneficiaryBanksIcon,
  BranchesIcon,
  ChargesIcon,
  CustomersIcon,
  HomeIcon,
  LogsIcon,
  ReportsIcon,
  RequestsApprovalIcon,
  RequestsIcon,
  SettingsIcon,
  StaffUsersIcon,
  TarrifsIcon,
  TransactionLimitsIcon,
} from '@/app/components/SvgIcons/SidebarIcons'
import { ACCESS_CONTROLS } from '@/app/const'
import { ISidebarConfigItem } from '@/app/interfaces/shared'

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Home',
    path: '/dashboard/home',
    icon: <HomeIcon />,
    app: 'dbp',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '2',
    title: 'Approval requests',
    path: '/dashboard/approval-requests',
    icon: <RequestsApprovalIcon />,
    app: 'dbp',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '3',
    title: 'User Management',
    path: '/dashboard/staff-users',
    icon: <StaffUsersIcon />,
    app: 'dbp',
    isProductionReady: true,
    requiredRights: [...ACCESS_CONTROLS.VIEW_USERS],
  },
  {
    id: '4',
    title: 'Customers',
    path: '/dashboard/customers',
    icon: <CustomersIcon />,
    app: 'dbp',
    isProductionReady: true,
    requiredRights: [...ACCESS_CONTROLS.VIEW_CUSTOMERS],
  },
  {
    id: '5',
    title: 'Transaction Limits',
    path: '/dashboard/transaction-limits',
    icon: <TransactionLimitsIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '6',
    title: 'Charges',
    path: '/dashboard/charges',
    icon: <ChargesIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '7',
    title: 'Tariffs',
    path: '/dashboard/tarrifs',
    icon: <TarrifsIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '8',
    title: 'Beneficiary Banks',
    path: '/dashboard/beneficiary-banks',
    icon: <BeneficiaryBanksIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '9',
    title: 'Branches',
    path: '/dashboard/branches',
    icon: <BranchesIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '10',
    title: 'Logs',
    path: '/dashboard/logs',
    icon: <LogsIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '11',
    title: 'Reports',
    path: '/dashboard/reports',
    icon: <ReportsIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '12',
    title: 'Audit Trail',
    path: '/dashboard/audit-trail',
    icon: <AuditTrailIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '13',
    title: 'Advertising',
    path: '/dashboard/advertising',
    icon: <AdvertisingIcon />,
    app: 'dbp',
    isProductionReady: false,
    requiredRights: [],
  },
  {
    id: '14',
    title: 'Home',
    path: '/lms/home',
    icon: <HomeIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '15',
    title: 'Reports',
    path: '/lms/reports',
    icon: <ReportsIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '16',
    title: 'Requests',
    path: '/lms/requests',
    icon: <RequestsIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '18',
    title: 'Organizations',
    path: '/lms/settings/organizations',
    icon: <ShieldOutlinedIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '19',
    title: 'Products',
    path: '/lms/settings/products',
    icon: <CustomProductsIcon width={'25'} height={'25'} />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '20',
    title: 'Brokers',
    path: '/lms/settings/brokers',
    icon: <CustomersIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
  {
    id: '17',
    title: 'Settings',
    path: '/lms/settings',
    icon: <SettingsIcon />,
    app: 'lms',
    isProductionReady: true,
    requiredRights: [],
  },
]
