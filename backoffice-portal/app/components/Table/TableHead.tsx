import {
  Box,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
} from '@mui/material'
import { visuallyHidden } from '@mui/utils'
import React from 'react'

import { IHeadCell } from '@/app/interfaces/shared'
import { CustomCheckBox } from '@/app/components/CheckBox'
import { CustomTableCell } from '@/app/dashboard/staff-users/roles/ListRoles'
type TableHeaderProps = {
  order: 'asc' | 'desc'
  orderBy: string
  rowCount: number
  headLabel: IHeadCell[]
  numSelected: number
  onRequestSort?: (event: React.MouseEvent<unknown>, property: string) => void
  onSelectAllClick?: (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean
  ) => void
  showCheckbox?: boolean
}

export const CustomTableHeader = (props: TableHeaderProps) => {
  const {
    order,
    orderBy,
    rowCount,
    headLabel,
    numSelected,
    onSelectAllClick,
    showCheckbox,
  } = props

  return (
    <TableHead sx={{ background: '#F9FAFB' }}>
      <TableRow>
        {showCheckbox && (
          <TableCell padding="checkbox">
            <CustomCheckBox
              indeterminate={numSelected > 0 && numSelected < rowCount}
              checked={rowCount > 0 && numSelected === rowCount}
              onChange={onSelectAllClick}
            />
          </TableCell>
        )}
        {headLabel.map((headCell) => (
          <CustomTableCell
            key={headCell.id}
            align={
              headCell.alignRight
                ? 'right'
                : headCell.alignCenter
                  ? 'center'
                  : 'left'
            }
            sortDirection={orderBy === headCell.id ? order : false}
          >
            <TableSortLabel
              hideSortIcon
              active={orderBy === headCell.id}
              direction={orderBy === headCell.id ? order : 'asc'}
              // onClick={createSortHandler(headCell.id)}
            >
              {headCell.label}
              {orderBy === headCell.id ? (
                <Box sx={{ ...visuallyHidden }}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box>
              ) : null}
            </TableSortLabel>
          </CustomTableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}
