import { Box, Button, Stack } from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import Arrow<PERSON><PERSON><PERSON>Icon from '@mui/icons-material/ArrowForward'
import React, { useState } from 'react'
interface PaginationProps {
  pageCount: number
  page: number
  handlePageClick: (page: number) => void
  handleForwardClick: () => void
  handleBackClick: () => void
}

export const CustomPagination = (props: PaginationProps) => {
  const {
    pageCount,
    handlePageClick,
    handleForwardClick,
    handleBackClick,
    page,
  } = props
  const [currentPage, setCurrentPage] = useState<number>(page)
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    handlePageClick(page)
  }
  const handleForward = () => {
    if (currentPage + 1 <= pageCount) {
      setCurrentPage(currentPage + 1)
      handleForwardClick()
      if (pageCount > 5) {
        if (
          firstPageStartIndex < currentPage + 1 &&
          firstPageStartIndex < pageCount - 3
        ) {
          setFirstPageStartIndex(firstPageStartIndex + 1)
        }
        if (lastPagesStartIndex < currentPage + 1) {
          setLastPagesStartIndex(lastPagesStartIndex + 1)
        }
      }
    }
  }
  const handleBack = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
      handleBackClick()
      if (pageCount > 5) {
        if (firstPageStartIndex > 1 && currentPage < firstPageStartIndex + 1) {
          setFirstPageStartIndex(
            firstPageStartIndex === 1 ? 1 : firstPageStartIndex - 3
          )
        }
        if (lastPagesStartIndex > 1 && currentPage < lastPagesStartIndex + 1) {
          setLastPagesStartIndex(lastPagesStartIndex - 3)
        }
      }
    }
  }
  const [firstPageStartIndex, setFirstPageStartIndex] = useState<number>(1)
  const [lastPagesStartIndex, setLastPagesStartIndex] = useState<number>(
    pageCount > 6 ? pageCount - 2 : 1
  )

  // Function to handle ellipsis click
  const handleEllipsisClick = () => {
    if (
      firstPageStartIndex + 3 <= pageCount &&
      currentPage < firstPageStartIndex + 3
    ) {
      setFirstPageStartIndex(firstPageStartIndex + 3)
      setLastPagesStartIndex(pageCount - 2)
      setCurrentPage(firstPageStartIndex + 3)
      handleForwardClick()
    } else if (
      lastPagesStartIndex + 3 <= pageCount &&
      currentPage > firstPageStartIndex + 1
    ) {
      setLastPagesStartIndex(lastPagesStartIndex - 3)
      setFirstPageStartIndex(1)
      setCurrentPage(lastPagesStartIndex - 3)
      handleBackClick()
    }
  }
  return (
    <Box
      sx={{
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        py: '1%',
        px: '1%',
      }}
    >
      <Stack>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          variant="outlined"
          sx={{
            border: '1px solid  #D0D5DD',
            color: '#344054',
            '.&:hover': {
              border: '1px solid  #D0D5DD',
            },
            fontWeight: 500,
          }}
        >
          Previous
        </Button>
      </Stack>
      <Stack flexDirection="row">
        {Array.from({ length: pageCount > 6 ? 3 : pageCount }, (_, index) => (
          <Button
            variant="outlined"
            sx={{
              border: 'none',
              color:
                currentPage === firstPageStartIndex + index
                  ? '#2A3339'
                  : '#667085',
              px: '0px',
              background:
                currentPage === firstPageStartIndex + index
                  ? '#F0F3F3'
                  : 'transparent',
              '&:hover': {
                border: 'none',
                boxShadow: 'none',
                background: '#667085',
              },
              fontWeight: '400',
              fontSize: '14px',
              marginRight: '3px',
            }}
            onClick={() => handlePageChange(firstPageStartIndex + index)}
            key={firstPageStartIndex + index}
          >
            {firstPageStartIndex + index}
          </Button>
        ))}
        {pageCount > 6 && lastPagesStartIndex > firstPageStartIndex ? (
          <Button onClick={handleEllipsisClick}>...</Button>
        ) : null}
        {pageCount > 6 &&
          lastPagesStartIndex > firstPageStartIndex &&
          Array.from({ length: 3 }, (_, index) => (
            <Button
              variant="outlined"
              sx={{
                border: 'none',
                color:
                  currentPage === lastPagesStartIndex + index
                    ? '#2A3339'
                    : '#667085',
                px: '0px',
                background:
                  currentPage === lastPagesStartIndex + index
                    ? '#F0F3F3'
                    : 'transparent',
                '&:hover': {
                  border: 'none',
                  boxShadow: 'none',
                  background: '#667085',
                },
                fontWeight: '400',
                fontSize: '14px',
                marginRight: '3px',
              }}
              onClick={() => handlePageChange(lastPagesStartIndex + index)}
              key={lastPagesStartIndex + index}
            >
              {lastPagesStartIndex + index}
            </Button>
          ))}
      </Stack>
      {/*</Stack>*/}
      <Stack>
        <Button
          endIcon={<ArrowForwardIcon />}
          variant="outlined"
          onClick={handleForward}
          sx={{
            border: '1px solid  #D0D5DD',
            fontWeight: 500,
            color: '#344054',
            '.&:hover': {
              border: '1px solid  #D0D5DD',
            },
          }}
        >
          Next
        </Button>
      </Stack>
    </Box>
  )
}
