'use client'
import {
  ChipProps as MuiChipProps,
  Stack,
  styled,
  Typography,
} from '@mui/material'
import { Chip } from '@mui/material'
import CircleIcon from '@mui/icons-material/Circle'
import RestoreIcon from '@mui/icons-material/Restore'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'

import { CHIPCOLORS } from '@/app/const'
import { IStatus } from '@/app/interfaces/shared'

interface ChipProps extends MuiChipProps {
  // Add any additional props here if needed
}
export const CustomStatusChip = styled(Chip)<ChipProps>(() => ({
  border: 'none',
  fontWeight: '600',
  padding: '2px 8px 2px 6px',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '6px',
}))

export const CustomSuccessChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={<CircleIcon sx={{ fontSize: '8px' }} color="success" />}
      variant="outlined"
      sx={{ color: '#027A48', backgroundColor: '#ECFDF3' }}
      {...props}
    />
  )
}
export const CustomActiveChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CircleIcon sx={{ fontSize: '8px', color: '#12B76A !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#027A48',
        backgroundColor: '#ECFDF3',
        fontWeight: 500,
        fontSize: '12px',
        height: '20px',
      }}
      {...props}
    />
  )
}
export const CustomWarningChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CircleIcon sx={{ fontSize: '8px', color: '#E16012 !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#E16012',
        backgroundColor: '#FFF6ED',
        fontWeight: 500,
        fontSize: '12px',
      }}
      {...props}
    />
  )
}

export const CustomErrorChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={<CircleIcon sx={{ fontSize: '8px' }} color="error" />}
      variant="outlined"
      sx={{
        color: '#B42318',
        backgroundColor: '#FEE4E2',
        fontWeight: 500,
        fontSize: '12px',
        height: '20px !important',
      }}
      {...props}
    />
  )
}

export const CustomLoanSuccessChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CheckCircleOutlinedIcon sx={{ fontSize: '12px' }} color={'info'} />
      }
      variant="outlined"
      sx={{ color: '#175CD3', backgroundColor: '#EFF8FF' }}
      {...props}
    />
  )
}

export const CustomLoanFailedChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <ErrorOutlineOutlinedIcon sx={{ fontSize: '12px' }} color={'error'} />
      }
      variant="outlined"
      sx={{ color: '#B42318', backgroundColor: '#FEF3F2' }}
      {...props}
    />
  )
}

export const CustomDrawerChip = styled(Chip)<ChipProps>(() => ({
  border: '1px solid #EAECF0',
  borderRadius: '6px',
  fontWeight: '500',
  padding: '2px 4px 2px 6px',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '6px',
  background: '#FFF',
  color: '##344054',
  fontSize: '12px',
  fontStyle: 'normal',
  textAlign: 'center',
  minWidth: '56px',
  height: '22px',
}))

export const CustomerStatusChip = styled(({ ...other }: ChipProps) => {
  const label: IStatus = other.label as IStatus
  const defaultStyles = {
    background: 'grey',
    color: 'white',
  }
  const chipStyles = CHIPCOLORS[label] || defaultStyles

  return (
    <Chip
      {...other}
      sx={{
        background: chipStyles.background,
        padding: '2px 6px 2px 8px',
        maxHeight: '20px',
        minWidth: '52px',
      }}
      label={
        <Stack
          sx={{
            gap: '6px',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          {' '}
          <Stack
            sx={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: chipStyles.color,
            }}
          ></Stack>
          <Typography
            variant="label2"
            sx={{
              color: chipStyles.color,
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 500,
              lineHeight: '16px',
            }}
          >
            {other.label &&
              (other.label as string).charAt(0) +
                (other.label as string).slice(1).toLowerCase()}
          </Typography>
        </Stack>
      }
    />
  )
})(() => ({
  padding: 0,
  textAlign: 'center',
  fontSize: '12px',
  fontStyle: 'normal',
  fontWeight: 500,
  lineHeight: '16px',
}))
export const CustomChip = styled(Chip)(() => {
  return {
    padding: '2px 6px',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '6px',
    border: '1px solid #EAECF0',
    height: '20px',
    fontSize: '12px',
    fontWeight: 400,
    color: '#344054',
    backgroundColor: '#FFFFFF',
  }
})
export const CustomerInfoChip = (props: {
  label: string
  requests: string[]
}) => {
  return (
    <Chip
      icon={
        <RestoreIcon sx={{ fontSize: '16px', color: '#555C61 !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#555C61',
        backgroundColor: '#F8F9FC',
        border: '1px solid #D5D9EB',
        padding: '6px 6px 6px 8px',
        maxHeight: '30px',
        minWidth: '52px',
      }}
      label={
        <Stack
          sx={{
            gap: '6px',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Typography
            variant="label2"
            sx={{
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: 500,
              lineHeight: '16px',
              color: '#555C61',
            }}
          >
            {props.label &&
              (props.label as string).charAt(0) +
                (props.label as string).slice(1).toLowerCase()}
          </Typography>
          {props.requests.map((item, index) => (
            <Typography
              key={index}
              variant="label2"
              sx={{
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: 500,
                lineHeight: '16px',
                textDecoration: 'underline',
                color: '#555C61',
              }}
            >
              {item}
            </Typography>
          ))}
        </Stack>
      }
    />
  )
}
