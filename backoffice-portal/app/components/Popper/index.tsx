// import {
//   Chip,
//   InputAdornment,
//   Paper,
//   Popover,
//   Portal,
//   Stack,
//   Typography,
// } from '@mui/material'
// import React, { Dispatch, SetStateAction, useState } from 'react'
// import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
// import { IPermission, IRole } from '@/app/interfaces/roles'

// import { CustomSearchInput } from '../Input/CustomSearchInput'

// export const ViewPermissions = ({
//   rights,
//   role,
//   setAnchorEl,
//   anchorEl,
// }: {
//   rights: IPermission[]
//   role: IRole
//   setAnchorEl: Dispatch<SetStateAction<null | HTMLElement>>
//   anchorEl: null | HTMLElement
// }) => {
//   const [open, setOpen] = useState<boolean>(false)
//   const [searchValue, setSearchValue] = useState<string>('')
//   const [filteredRights, setFilteredRights] = useState<IPermission[]>(rights)

//   const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchValue(event.target.value)
//     const filtered: IPermission[] = rights.filter((rights: IPermission) => {
//       return rights.name
//         .toLowerCase()
//         .includes(event.target.value.toLowerCase())
//     })
//     if (!event.target.value) {
//       setFilteredRights(rights)
//     }
//     setFilteredRights(filtered)
//   }
//   const handleClose = () => {
//     setAnchorEl(null)
//     setOpen(false)
//   }
//   return (
//     <>
//       <Portal>
//         <Popover
//           id={role.id}
//           open={open}
//           anchorEl={anchorEl}
//           onClose={handleClose}
//           anchorOrigin={{
//             vertical: 'bottom',
//             horizontal: 'left',
//           }}
//         >
//           <Paper
//             elevation={1}
//             sx={{
//               padding: '5%',
//             }}
//           >
//             <Stack>
//               <Typography
//                 sx={{
//                   fontWeight: '600',
//                   fontSize: '16px',
//                   color: '#101828',
//                 }}
//               >
//                 {role.name}({10})
//               </Typography>
//               <Typography
//                 sx={{
//                   fontWeight: '400',
//                   fontSize: '14px',
//                 }}
//               >
//                 The following permissions have been assigned to this
//                 designation.
//               </Typography>
//             </Stack>
//             <CustomSearchInput
//               sx={{
//                 height: '50px',
//                 my: '10px',
//               }}
//               value={searchValue}
//               onChange={handleSearch}
//               placeholder="Search"
//               endAdornment={
//                 <InputAdornment position="start">
//                   <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
//                 </InputAdornment>
//               }
//             />
//             <Stack
//               sx={{
//                 display: 'flex',
//                 flexDirection: 'column',
//                 gap: '5px',
//                 marginTop: '5%',
//                 height: '30vh',
//                 overflowY: 'scroll',
//               }}
//             >
//               {filteredRights &&
//                 filteredRights.map((permission: IPermission) => (
//                   <Chip
//                     key={permission.id}
//                     label={permission.name}
//                     sx={{
//                       color: '#363F72',
//                       background: '#F8F9FC',
//                       fontWeight: '500',
//                       fontSize: '13px',
//                       textAlign: 'left',
//                       py: '5px',
//                       width: '60%',
//                       borderRadius: '16px',
//                       justifyContent: 'flex-start',
//                     }}
//                   />
//                 ))}
//             </Stack>
//           </Paper>
//         </Popover>
//       </Portal>
//     </>
//   )
// }
