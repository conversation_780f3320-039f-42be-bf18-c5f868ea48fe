import { styled, Tooltip, tooltipClasses, TooltipProps } from '@mui/material'
import React from 'react'

export const Tooltips = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(() => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: '#FFF',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#FFF',
    color: '#000000',
    boxShadow:
      '0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)',
    right: '50px',
    maxWidth: '110px',
    cursor: 'pointer',
  },
}))

export const SidebarTooltip = styled(
  ({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} arrow classes={{ popper: className }} />
  )
)(() => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: '#2A3339',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: '#2A3339',
    color: '#FFF',
    boxShadow:
      '0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)',
    right: '5px',
    minWidth: '90px',
    padding: '8px 12px',
    fontSize: '12px',
    lineHeight: '16px',
    fontWeight: 400,
  },
}))
