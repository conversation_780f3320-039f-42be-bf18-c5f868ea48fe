import React from 'react'
import { Grow, GrowProps } from '@mui/material'

interface IGrowProviderProps {
  children?: GrowProps['children']
  in: boolean
}

const GrowProvider: React.FC<IGrowProviderProps> = ({ children, in: isIn }) => {
  if (!children) {
    return null
  }

  return (
    <Grow in={isIn} {...(isIn ? { timeout: 600 } : {})}>
      {children}
    </Grow>
  )
}

export default GrowProvider
