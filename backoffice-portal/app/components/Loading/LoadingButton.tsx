import { Button, CircularProgress } from '@mui/material'
interface LoadingButtonProps {
  width?: string
  height?: string
}
export const LoadingButton = (props: LoadingButtonProps) => {
  return (
    <Button
      variant="contained"
      disabled
      fullWidth
      sx={{
        py: '1%',
        background: '#EAECF0',
        width: props.width ? props.width : '100%',
        height: props.height ? props.height : '100%',
        borderRadius: '6px',
      }}
    >
      <CircularProgress color="primary" size={30} thickness={3.0} />
    </Button>
  )
}
