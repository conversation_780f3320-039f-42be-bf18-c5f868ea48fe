export const PersonalDetailsIcon = () => {
  return (
    <svg
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_3313_122725)">
        <path
          d="M2.5 11C2.5 5.75329 6.75329 1.5 12 1.5H40C45.2467 1.5 49.5 5.75329 49.5 11V39C49.5 44.2467 45.2467 48.5 40 48.5H12C6.75329 48.5 2.5 44.2467 2.5 39V11Z"
          stroke="#E3E4E4"
          shapeRendering="crispEdges"
        />
        <path
          d="M34 34C34 32.6044 34 31.9067 33.8278 31.3389C33.44 30.0605 32.4395 29.06 31.1611 28.6722C30.5933 28.5 29.8956 28.5 28.5 28.5H23.5C22.1044 28.5 21.4067 28.5 20.8389 28.6722C19.5605 29.06 18.56 30.0605 18.1722 31.3389C18 31.9067 18 32.6044 18 34M30.5 20.5C30.5 22.9853 28.4853 25 26 25C23.5147 25 21.5 22.9853 21.5 20.5C21.5 18.0147 23.5147 16 26 16C28.4853 16 30.5 18.0147 30.5 20.5Z"
          stroke="#2A3339"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_3313_122725"
          x="0"
          y="0"
          width="52"
          height="52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3313_122725"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3313_122725"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
export const AccountsIcon = () => {
  return (
    <svg
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_3378_32189)">
        <path
          d="M2.5 11C2.5 5.75329 6.75329 1.5 12 1.5H40C45.2467 1.5 49.5 5.75329 49.5 11V39C49.5 44.2467 45.2467 48.5 40 48.5H12C6.75329 48.5 2.5 44.2467 2.5 39V11Z"
          stroke="#EAECF0"
          shapeRendering="crispEdges"
        />
        <path
          d="M29.9377 28.9377C33.3603 28.4795 36 25.548 36 22C36 18.134 32.866 15 29 15C25.452 15 22.5205 17.6397 22.0623 21.0623M30 28C30 31.866 26.866 35 23 35C19.134 35 16 31.866 16 28C16 24.134 19.134 21 23 21C26.866 21 30 24.134 30 28Z"
          stroke="#808488"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_3378_32189"
          x="0"
          y="0"
          width="52"
          height="52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3378_32189"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3378_32189"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
export const SummaryIcon = () => {
  return (
    <svg
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_3378_32198)">
        <path
          d="M2.5 11C2.5 5.75329 6.75329 1.5 12 1.5H40C45.2467 1.5 49.5 5.75329 49.5 11V39C49.5 44.2467 45.2467 48.5 40 48.5H12C6.75329 48.5 2.5 44.2467 2.5 39V11Z"
          stroke="#EAECF0"
          shapeRendering="crispEdges"
        />
        <path
          d="M24.4995 26.5002L34.9995 16.0002M24.6271 26.8282L27.2552 33.5862C27.4867 34.1816 27.6025 34.4793 27.7693 34.5662C27.9139 34.6415 28.0862 34.6416 28.2308 34.5664C28.3977 34.4797 28.5139 34.1822 28.7461 33.5871L35.3364 16.6994C35.5461 16.1622 35.6509 15.8936 35.5935 15.722C35.5437 15.5729 35.4268 15.456 35.2777 15.4062C35.1061 15.3488 34.8375 15.4536 34.3003 15.6633L17.4126 22.2536C16.8175 22.4858 16.52 22.602 16.4333 22.7689C16.3581 22.9135 16.3582 23.0858 16.4335 23.2304C16.5204 23.3972 16.8181 23.513 17.4135 23.7445L24.1715 26.3726C24.2923 26.4196 24.3527 26.4431 24.4036 26.4794C24.4487 26.5115 24.4881 26.551 24.5203 26.5961C24.5566 26.647 24.5801 26.7074 24.6271 26.8282Z"
          stroke="#808488"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_3378_32198"
          x="0"
          y="0"
          width="52"
          height="52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3378_32198"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3378_32198"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
export const ConnectorIcon = () => {
  return (
    <svg
      width="2"
      height="26"
      viewBox="0 0 2 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 1C0 0.447716 0.447715 0 1 0C1.55228 0 2 0.447715 2 1V25C2 25.5523 1.55228 26 1 26C0.447715 26 0 25.5523 0 25V1Z"
        fill="#EAECF0"
      />
    </svg>
  )
}
