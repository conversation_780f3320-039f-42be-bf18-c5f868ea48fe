export const EmptyFolder = () => {
  return (
    <svg
      width="117"
      height="118"
      viewBox="0 0 117 118"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect width="117" height="118" fill="url(#pattern0_290_4052)" />
      <defs>
        <pattern
          id="pattern0_290_4052"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use
            xlinkHref="#image0_290_4052"
            transform="matrix(0.00196982 0 0 0.00195312 -0.0042735 0)"
          />
        </pattern>
        <image
          id="image0_290_4052"
          width="512"
          height="512"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  )
}
