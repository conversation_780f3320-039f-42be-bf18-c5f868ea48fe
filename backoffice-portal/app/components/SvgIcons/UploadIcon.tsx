export const UploadIcon = () => {
  return (
    <svg
      width="45"
      height="44"
      viewBox="0 0 45 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_2164_10752)">
        <path
          d="M3 9C3 4.85786 6.35786 1.5 10.5 1.5H34.5C38.6421 1.5 42 4.85786 42 9V33C42 37.1421 38.6421 40.5 34.5 40.5H10.5C6.35786 40.5 3 37.1421 3 33V9Z"
          stroke="#EAECF0"
          shapeRendering="crispEdges"
        />
        <path
          d="M19.1665 24.3333L22.4998 21M22.4998 21L25.8332 24.3333M22.4998 21V28.5M29.1665 24.9524C30.1844 24.1117 30.8332 22.8399 30.8332 21.4167C30.8332 18.8854 28.7811 16.8333 26.2498 16.8333C26.0677 16.8333 25.8974 16.7383 25.8049 16.5814C24.7182 14.7374 22.7119 13.5 20.4165 13.5C16.9647 13.5 14.1665 16.2982 14.1665 19.75C14.1665 21.4718 14.8627 23.0309 15.989 24.1613"
          stroke="#344054"
          strokeWidth="1.66667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_2164_10752"
          x="0.5"
          y="0"
          width="44"
          height="44"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2164_10752"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2164_10752"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
