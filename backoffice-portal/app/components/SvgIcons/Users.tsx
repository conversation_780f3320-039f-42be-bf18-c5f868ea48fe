export const UsersIcon = () => {
  return (
    <svg
      width="21"
      height="22"
      viewBox="0 0 21 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.3262 10.9451C12.4585 10.9451 13.3764 11.863 13.3764 12.9953V14.533C13.3764 16.639 11.2359 18.1209 7.73824 18.1209C4.24059 18.1209 2.1001 16.639 2.1001 14.533V12.9953C2.1001 11.863 3.01802 10.9451 4.15033 10.9451H11.3262ZM16.4517 10.9451C17.5841 10.9451 18.502 11.863 18.502 12.9953V13.5079C18.502 15.6489 16.8943 17.0958 13.8889 17.0958C13.7441 17.0958 13.6025 17.0924 13.4641 17.0858C14.0171 16.4423 14.3439 15.6649 14.3946 14.7777L14.4015 14.533V12.9953C14.4015 12.286 14.1614 11.6328 13.758 11.1126L13.6176 10.9442L16.4517 10.9451ZM7.73824 2.74414C9.71979 2.74414 11.3262 4.3505 11.3262 6.33205C11.3262 8.3136 9.71979 9.91996 7.73824 9.91996C5.7567 9.91996 4.15033 8.3136 4.15033 6.33205C4.15033 4.3505 5.7567 2.74414 7.73824 2.74414ZM14.9141 4.79438C16.3295 4.79438 17.4769 5.94178 17.4769 7.35717C17.4769 8.77256 16.3295 9.91996 14.9141 9.91996C13.4987 9.91996 12.3513 8.77256 12.3513 7.35717C12.3513 5.94178 13.4987 4.79438 14.9141 4.79438Z"
        fill="none"
        stroke="#5C6670"
        strokeWidth="1.5"
      />
    </svg>
  )
}

export const UserProfileIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="30"
      height="31"
      viewBox="0 0 30 31"
      fill="none"
    >
      <path
        d="M25 26.75V24.25C25 22.9239 24.4732 21.6521 23.5355 20.7145C22.5979 19.7768 21.3261 19.25 20 19.25H10C8.67392 19.25 7.40215 19.7768 6.46447 20.7145C5.52678 21.6521 5 22.9239 5 24.25V26.75M20 9.25C20 12.0114 17.7614 14.25 15 14.25C12.2386 14.25 10 12.0114 10 9.25C10 6.48858 12.2386 4.25 15 4.25C17.7614 4.25 20 6.48858 20 9.25Z"
        stroke="#2A3339"
        strokeWidth="1.97368"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CustomerSettingsIcon = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="28" height="28" rx="2.947" fill="#E7E8E9" />
      <g clipPath="url(#clip0_3318_130321)">
        <path
          d="M17.4125 19.8609V18.6359C17.4125 17.9862 17.1544 17.363 16.695 16.9035C16.2355 16.4441 15.6123 16.1859 14.9625 16.1859H10.0625C9.41277 16.1859 8.7896 16.4441 8.33014 16.9035C7.87067 17.363 7.61255 17.9862 7.61255 18.6359V19.8609M21.0875 19.8609V18.6359C21.0871 18.0931 20.9065 17.5658 20.5739 17.1367C20.2413 16.7077 19.7757 16.4013 19.25 16.2656M16.8 8.91556C17.3271 9.0505 17.7942 9.35699 18.1277 9.78673C18.4613 10.2165 18.6424 10.745 18.6424 11.289C18.6424 11.833 18.4613 12.3615 18.1277 12.7913C17.7942 13.221 17.3271 13.5275 16.8 13.6624M14.9625 11.2859C14.9625 12.639 13.8656 13.7359 12.5125 13.7359C11.1595 13.7359 10.0625 12.639 10.0625 11.2859C10.0625 9.93284 11.1595 8.83594 12.5125 8.83594C13.8656 8.83594 14.9625 9.93284 14.9625 11.2859Z"
          stroke="#2A3339"
          strokeWidth="0.967105"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3318_130321">
          <rect
            width="14.7"
            height="14.7"
            fill="white"
            transform="translate(7 7)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
