'use client'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { Suspense } from 'react'

import { useCustomRouter } from '@/app/utils/helpers'
import { handleLogin } from '@/app/redux/actions/auth'
import { LoadingFullScreen } from '@/app/components/Loading'
import { useAppDispatch } from '@/app/redux'
import { setNotification } from '@/app/redux/reducers/notifications'

export default function AuthenticatePage() {
  const searchParams = useSearchParams()
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const accessTokenObject = searchParams.get('accessToken') || ''
  useEffect(() => {
    const tokenObject = JSON.parse(accessTokenObject)
    if (tokenObject.success === true) {
      handleLogin(tokenObject, dispatch, router)
    } else if (tokenObject.success === false) {
      dispatch(
        setNotification({ type: 'error', message: tokenObject.statusMessage })
      )
      router.push('/')
    }
  }, [accessTokenObject])
  return (
    <Suspense>
      <LoadingFullScreen />
    </Suspense>
  )
}
