'use client'
import { Box, Button, Typography } from '@mui/material'
import Image from 'next/image'

import { useCustomRouter } from '@/app/utils/helpers'
import { useAppDispatch, useAppSelector } from '@/app/redux'
import { LoadingButton } from '@/app/components/Loading/LoadingButton'
import { setIsLoadingLogin } from '@/app/redux/reducers/auth'

export const LoginForm = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const isLoadingLogin = useAppSelector((state) => state.auth.isLoadingLogin)

  const handleLogin = async () => {
    dispatch(setIsLoadingLogin(true))
    router.push(
      `${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/login/authenticate`
    )
    dispatch(setIsLoadingLogin(false))
  }

  return (
    <>
      <Box
        sx={{
          backgroundImage: 'url("bg-login-pattern.svg")',
          backgroundSize: '816px 716px',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'absolute',
          backgroundPositionX: '59px',
          backgroundPositionY: '0px',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
          }}
        >
          {/* text label area  */}

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '6px',
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight: '300',
              }}
            >
              Hi there.
            </Typography>
            <Typography
              variant="h3"
              sx={{
                fontWeight: '400',
              }}
            >
              Welcome to DTBx Back Office Portal
            </Typography>
          </Box>
          {isLoadingLogin ? (
            <LoadingButton />
          ) : (
            <Button
              onClick={handleLogin}
              variant="outlined"
              sx={{
                width: '100%',
              }}
            >
              <Image src="ms-logo.svg" alt="ms-logo" width={20} height={20} />
              <Typography
                variant="subtitle2"
                sx={{
                  textTransform: 'none',
                }}
              >
                Sign in with Microsoft
              </Typography>
            </Button>
          )}
        </Box>
      </Box>
    </>
  )
}
