'use client'

import { Box } from '@mui/material'

import { Logo } from '@/app/components/Logo'
import { LoginForm } from '@/app/auth/login/LoginForm'

export default function LoginPage() {
  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100%',
      }}
    >
      <Box
        sx={{
          width: '50%',
          background:
            'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%), url("landingBG.svg"), lightgray',
          backgroundPosition: 'left',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          paddingLeft: '35px',
          paddingTop: '22px',
        }}
      >
        <Logo />
      </Box>
      <Box
        sx={{
          width: '50%',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <LoginForm />
      </Box>
    </Box>
  )
}
