'use client'
import React from 'react'

import ReduxProvider from '@/app/redux/ReduxProvider'
import AuthWrapper from '@/app/components/AuthWrapper'
import LocalNotification from '@/app/components/SnackBar'

export default function AuthLayout(props: { children: React.ReactNode }) {
  return (
    <AuthWrapper requiresAuth={false}>
      <ReduxProvider>
        <LocalNotification />
        {props.children}
      </ReduxProvider>
    </AuthWrapper>
  )
}
