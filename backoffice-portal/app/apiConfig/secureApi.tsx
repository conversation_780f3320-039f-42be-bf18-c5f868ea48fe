import axios, { AxiosError, AxiosResponse } from 'axios'
import { jwtDecode } from 'jwt-decode'

import { refreshToken } from '@/app/redux/actions/auth'

import { authConfig, authConfig2 } from './authHeader'
import { IDecodeToken } from '../interfaces/users'
const instance1 = axios.create(authConfig)
const instance2 = axios.create(authConfig2)

class APIError extends Error {
  private statusCode: number
  constructor(statusCode: number, message: string) {
    super(message)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, APIError)
    }
    this.statusCode = statusCode
  }
}

interface ErrorResponse {
  message?: string
  error?: string
  errors?: string
  status: number
  data: {
    message?: string
    error?: string
    errors?: string
  }
}
const errorHandler = (err: AxiosError<ErrorResponse>) => {
  let message = 'Something went wrong'
  let statusCode = 400

  if (err.response) {
    statusCode = err.response.status
    message = err.response.data.message || message
    if (statusCode === 401) {
      message = 'You are not allowed to perform this request'
    }
    if (statusCode === 403) {
      message = err.response.data.message || 'Access Denied!!'
    }
    if (statusCode === 404) {
      message =
        err.response.data.errors?.[0] ||
        err.response.data.message ||
        'Resource not found'
    }
    if (statusCode === 400) {
      message = err.response.data.error
        ? err.response.data.error
        : err.response.data.message || message
    }
  } else if (err.request) {
    statusCode = 503
    message = 'No response from server'
    console.error(message)
  }

  return new APIError(statusCode, message)
}

async function refreshTokenIfNeeded(): Promise<string | null> {
  const token = localStorage.getItem('accessToken')
    ? localStorage.getItem('accessToken')
    : ''
  if (
    !token ||
    jwtDecode<IDecodeToken>(token).exp * 1000 < new Date().getTime()
  ) {
    await refreshToken()
  }
  return token
}

// Set the AUTH token for any request
instance1.interceptors.request.use(async (config) => {
  const token = await refreshTokenIfNeeded()
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

instance1.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)
instance2.interceptors.request.use(async (config) => {
  const token = await refreshTokenIfNeeded()
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

instance2.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)

export const secureapi = instance1
export const secureapi2 = instance2
