import axios, { AxiosError, AxiosInstance } from 'axios'

import { authConfig } from './authHeader'
const openAuth = {
  ...authConfig,
  baseURL: process.env.NEXT_PUBLIC_OPEN_API_BASE_URL,
}
const openAuth2 = {
  ...authConfig,
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
}
const instance: AxiosInstance = axios.create(openAuth)
const instance2: AxiosInstance = axios.create(openAuth2)

class APIError extends Error {
  public statusCode: number
  public description: string

  constructor(status: number, message: string, description: string) {
    super(message)

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, APIError)
    }

    this.statusCode = status
    this.description = description
    this.message = message
  }
}

interface ErrorResponse {
  status: string
  message: string
  description: string
}

const errorHandler = (err: AxiosError<ErrorResponse>) => {
  try {
    if (err.response) {
      const { status, data } = err.response
      console.error(data.message || 'Something went wrong')
      throw new APIError(status, data.message, data.description)
    } else if (err.request) {
      throw new APIError(503, 'No response from server', '')
    } else {
      throw new APIError(400, 'Unknown error occurred', '')
    }
  } catch (e) {
    return e instanceof Error ? e : new Error('Unknown error occurred')
  }
}

instance.interceptors.response.use(
  (response) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)

export const openapi = instance
export const openapi2 = instance2
