import React, { FocusEvent, useEffect, useMemo } from 'react'
import {
  Select,
  MenuItem,
  SelectChangeEvent,
  FormControl,
  FormHelperText,
} from '@mui/material'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'
import { getAuctionSchedule } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { AuctionSchedule } from '@/store/interfaces'
import { getAuctionWeek } from '@dtbx/store/utils'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export interface CustomDateSelectPickerProps {
  year?: string
  value: string
  onChange: (schedule: AuctionSchedule) => void
  touched?: boolean
  error?: string
  helperText?: string
  onBlur?: (
    e: FocusEvent<HTMLInputElement | HTMLTextAreaElement, Element>
  ) => void
  required?: boolean
  placeholder?: string
  slotProps?: {
    textField?: {
      sx?: Record<string, any>
      [key: string]: any
    }
  }
  slots?: {
    openPickerIcon?: () => React.JSX.Element
  }
  sx?: Record<string, any>
}

export const AuctionPicker: React.FC<CustomDateSelectPickerProps> = ({
  year = new Date().getFullYear().toString(),
  value,
  onChange,
  touched = false,
  error,
  helperText,
  onBlur,
  required = false,
  placeholder = 'Select Sale Date',
  slotProps,
  slots,
  sx,
  ...props
}) => {
  const { auctionScheduleResponse } = useAppSelector(
    (state) => state.salesSchedule
  )
  const { data } = auctionScheduleResponse
  const dispatch = useAppDispatch()
  const isBackoffice = useMemo(() => checkIfBackOffice(), [])
  const handleOnChange = (event: SelectChangeEvent) => {
    const schedule = data.find(
      (schedule) => schedule.saleCode === event.target.value
    )
    if (!schedule) return

    onChange(schedule)
  }

  const setSaleBasedOnCurrentWeek = () => {
    if (!value) {
      const week = getAuctionWeek().toString()
      const schedule = data.find(
        (schedule) => schedule.saleCode === week.toString()
      )

      if (schedule) onChange(schedule)
    }
  }

  useEffect(() => {
    getAuctionSchedule(dispatch, isBackoffice, {
      year,
      page: 1,
      size: 55,
    })
  }, [year, dispatch, isBackoffice])

  useEffect(() => {
    setSaleBasedOnCurrentWeek()
  }, [data, value])

  return (
    <FormControl fullWidth error={touched && Boolean(error)}>
      <Select
        required={required}
        fullWidth
        value={value}
        onChange={handleOnChange}
        onBlur={onBlur}
        displayEmpty
        error={touched && Boolean(error)}

        IconComponent={(iconProps) =>
          slots?.openPickerIcon ? (
            slots.openPickerIcon() 
          ) : (
            <KeyboardArrowDownRounded
              {...iconProps} 
              sx={{
                color: '#667085',
                marginleft: '0.5rem',
                ...(iconProps?.sx || {}),
              }}
            />
          )
        }
        {...props}
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          color: '#667085',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '7px !important',
          },
          ...(sx || {}),
          ...(slotProps?.textField?.sx || {}),
        }}
      >
        <MenuItem value="" disabled>
          {placeholder}
        </MenuItem>
        {data.map((schedule, index) => (
          <MenuItem 
            key={`${schedule.year}-${schedule.saleCode}-${schedule.id || index}`} 
            value={schedule.saleCode}
          >
            {schedule.year}/{schedule.saleCode}
          </MenuItem>
        ))}
      </Select>
      {touched && error && <FormHelperText>{error}</FormHelperText>}
    </FormControl>
  )
}
