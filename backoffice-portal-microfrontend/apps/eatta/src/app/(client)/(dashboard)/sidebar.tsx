import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import {
  CompaniesIcon,
  PaymentsPageIcon,
  ReceiptIcon,
  StatementsIcon,
} from '@dtbx/ui/icons'
import { ClientType } from '@dtbx/store/interfaces'
import { InvoiceIcon } from '@/components/SvgIcons/InvoiceIcon'
import {
  PostAuctionIcon,
  PreAuctionIcon,
} from '@/components/SvgIcons/AuctionIcon'
import { BankIcon } from '@/components/SvgIcons/BankIcon'

const insightSideBar: ISidebarConfigItem = {
  id: '1',
  title: 'Insights',
  path: '/insights',
  module: 'default',
  icon: <PaymentsPageIcon />,
  isProductionReady: true,
}
export const producerSideConfig = [
  {
    id: '4',
    title: 'Account Sales',
    path: '/account-sales',
    module: 'default',
    icon: <ReceiptIcon />,
    isProductionReady: true,
  },
  {
    id: '5',
    title: 'Statement',
    path: '/statement',
    module: 'default',
    icon: <StatementsIcon />,
    isProductionReady: true,
  },
  {
    id: '6',
    title: 'Disbursements',
    path: '/disbursements',
    module: 'default',
    icon: <BankIcon />,
    isProductionReady: true,
  },
  {
    id: '7',
    title: 'Factories',
    path: '/factories',
    module: 'default',
    icon: <CompaniesIcon stroke="#667085" />,
    isProductionReady: true,
  },
]
const baseSidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Pre-sale',
    path: '/pre-auction',
    module: 'default',
    icon: <PreAuctionIcon />,
    isProductionReady: true,
  },
  {
    id: '2',
    title: 'Sales',
    path: '/post-auction',
    module: 'default',
    icon: <PostAuctionIcon />,
    isProductionReady: true,
  },
  {
    id: '3',
    title: 'Invoices',
    path: '/invoices?tab=0',
    module: 'default',
    icon: <InvoiceIcon />,
    isProductionReady: true,
  },
  {
    id: '4',
    title: 'Statement',
    path: '/statement',
    module: 'default',
    icon: <StatementsIcon />,
    isProductionReady: true,
  },
  {
    id: '5',
    title: 'Delivery Orders',
    path: '/delivery-orders',
    module: 'default',
    icon: <PostAuctionIcon />,
    isProductionReady: true,
  },
]

export const getSidebarConfig = (
  clientType: ClientType | undefined
): ISidebarConfigItem[] => {
  if (clientType === 'Partner') {
    return [insightSideBar, ...baseSidebarConfig.slice(0, 2)]
  }

  if (clientType === 'Warehouse') {
    return baseSidebarConfig.slice(0, 2)
  }

  if (clientType === 'Producer') {
    return [
      // insightSideBar,
      ...baseSidebarConfig.filter(
        (item) =>
          item.title !== 'Invoices' &&
          item.title !== 'Transaction History' &&
          item.title !== 'Statement' &&
          item.title !== 'Delivery Orders'
      ),
      ...producerSideConfig,
    ]
  }

  if(clientType === 'Buyer'){
    return [
      ...baseSidebarConfig.filter(
        (item) =>
          item.title !== 'Delivery Orders'
      )
    ]
  }
  return baseSidebarConfig
}
