{"name": "boma-yangu-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3010", "build": "next build", "start": "next start --port 3010", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@dtbx/store": "^0.0.1", "@dtbx/ui": "^0.0.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/x-date-pickers": "^7.23.1", "@reduxjs/toolkit": "^2.8.2", "dayjs": "^1.11.13", "formik": "^2.4.6", "mui-tel-input": "^9.0.1", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tiny-case": "^1.0.3", "yup": "^1.6.1"}, "devDependencies": {"@dtbx/eslint-config": "^0.0.1", "@dtbx/typescript-config": "^0.0.1", "@dtbx/vitest-config": "^0.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "22.15.29", "@types/react": "19.1.6", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-istanbul": "3.2.1", "jsdom": "^26.1.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9"}}