"use client";
import React, { ReactNode } from "react";
import { Box } from "@mui/material";
import { NextAppDirEmotionCacheProvider, ThemeConfig } from "@dtbx/ui/theme";
import "./fonts.css";
import { useAppSelector, useAppDispatch } from "@dtbx/store";
import { refreshToken } from "@dtbx/store/actions";
import {
  clearNotification,
  setDocumentToggle,
  setSidebarCollapsed,
} from "@dtbx/store/reducers";
import AppProvider from "@/store/AppProvider";
import {
  AuthWrapper,
  CustomScrollbar,
  InActivity,
  LocalNotification,
} from "@dtbx/ui/components";
import { IDView } from "@dtbx/ui/components/Overlay";
import { Sidebar } from "@dtbx/ui/components/Sidebar";
import { InternalNavBar } from "@dtbx/ui/components/Appbar";

import { isLoggedIn } from "@dtbx/store/utils";
import { sidebarConfig } from "./sidebar";

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: "mui" }}>
            <ThemeConfig themeType="main">
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>
                  <DashboardLayout>{children}</DashboardLayout>
                </InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  );
}

function DashboardLayout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch();
  const { isSidebarCollapsed, documentToggle } = useAppSelector(
    (state) => state.navigation
  );
  const profile = useAppSelector((state) => state.auth.decodedToken);
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  );
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    "info";
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{ height: "100%", display: "flex", flexDirection: "row" }}>
        <Sidebar
          bgColor="#FFFFFF"
          sidebarConfig={sidebarConfig}
          sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
        />
        <Box
          sx={{
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
            width: isSidebarCollapsed ? "95vw" : "88vw",
          }}
        >
          <InternalNavBar profile={profile} refreshToken={refreshToken} />
          <Box
            sx={{
              overflow: "hidden",
              width: "100%",
              height: "100%",
              backgroundColor: "#F2F4F7",
            }}
          >
            <LocalNotification
              clearNotification={() => dispatch(clearNotification())}
              notification={notification}
              notificationType={notificationType}
            />
            {children}
          </Box>
        </Box>
      </Box>
      <IDView
        open={documentToggle}
        onClose={() => dispatch(setDocumentToggle(false))}
      />
    </AuthWrapper>
  );
}
